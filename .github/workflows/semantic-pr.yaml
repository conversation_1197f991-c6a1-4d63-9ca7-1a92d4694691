name: "Semantic pull requests"

on:
  pull_request:
    types:
      - opened
      - edited
      - labeled
      - unlabeled
      - converted_to_draft
      - ready_for_review
      - synchronize

jobs:
  main:
    name: Validate PR title
    runs-on: ubuntu-latest
    steps:
      - 
        uses: amannn/action-semantic-pull-request@v5
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          types: |
            fix
            feat
            chore
            refactor
            exp
            doc
            test
          scopes: |
            core
            multi-tenant
            tooling
            gateway
            jobsdb
            warehouse
            processor
            router
            batchrouter
            destination
            startup
            shutdown
            ci
          requireScope: false
          subjectPattern: ^(?![A-Z]).+$
          subjectPatternError: |
            The subject "{subject}" found in the pull request title "{title}"
            didn't match the configured pattern. Please ensure that the subject
            doesn't start with an uppercase character.
          # For work-in-progress PRs you can typically use draft pull requests 
          # from GitHub. However, private repositories on the free plan don't have 
          # this option and therefore this action allows you to opt-in to using the 
          # special "[WIP]" prefix to indicate this state. This will avoid the 
          # validation of the PR title and the pull request checks remain pending.
          # Note that a second check will be reported if this is enabled.
          wip: true
          # When using "Squash and merge" on a PR with only one commit, GitHub
          # will suggest using that commit message instead of the PR title for the
          # merge commit, and it's easy to commit this by mistake. Enable this option
          # to also validate the commit message for one commit PRs.
          validateSingleCommit: false
          # Related to `validateSingleCommit` you can opt-in to validate that the PR
          # title matches a single commit to avoid confusion.
          validateSingleCommitMatchesPrTitle: false
          # If the PR contains one of these labels, the validation is skipped.
          # Multiple labels can be separated by newlines.
          # If you want to rerun the validation when labels change, you might want
          # to use the `labeled` and `unlabeled` event triggers in your workflow.
          ignoreLabels: |
            bot
            dependencies
