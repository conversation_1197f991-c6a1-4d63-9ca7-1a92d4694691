name: verify
on:
  push:
    tags:
      - v*
    branches:
      - master
      - main
  pull_request:
concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref || github.sha }}
  cancel-in-progress: true
jobs:
  generate:
    name: Correct generated files
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-go@v5
        with:
          go-version-file: 'go.mod'
      - run: go version

      - run: go mod tidy
      - run: git diff --exit-code go.mod
      - name: Error message
        if: ${{ failure() }}
        run: echo '::error file=go.mod,line=1,col=1::Inconsistent go mod file. Ensure you have run `go mod tidy` and committed the files locally.'; echo '::error file=enterprise_mod.go,line=1,col=1::Possible missing enterprise exclusive dependencies.'

      - run: make mocks
      - run: git diff --exit-code
      - name: Error message
        if: ${{ failure() }}
        run: echo '::error file=Makefile,line=11,col=1::Incorrectly generated files. Ensure you have run `make mocks` and committed the files locally.'

      - name: install protoc compiler
        uses: arduino/setup-protoc@v3
        with:
          version: '25.x'
          repo-token: ${{ secrets.GITHUB_TOKEN }}
      - run: make proto
      - run: git diff -I '^\/\/\s+-?\s+protoc\s+v' --exit-code ## Ignore protoc version comment
      - name: Error message
        if: ${{ failure() }}
        run: echo 'proto files are not generated correctly. Ensure you have run `make proto` and committed the files locally.'
      - run: git checkout proto ## cleanup tree due to protoc version comment

      - run: make fmt
      - run: git diff --exit-code
      - name: Error message
        if: ${{ failure() }}
        run: echo 'Not formatted files. Ensure you have run `make fmt` and committed the files locally.'
      - name: Validate OpenAPI definition
        uses: char0n/apidom-validate@v1
        with:
          definition-file: gateway/openapi.yaml

      - name: Login to Docker Hub
        uses: docker/login-action@v3.4.0
        with:
          username: rudderlabs
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - run: make generate-openapi-spec
      - run: git diff --exit-code
      - name: Error message
        if: ${{ failure() }}
        run: echo 'Not correctly generated openapi spec. Ensure you have run `make generate-openapi-spec` and committed the files locally.'
  linting:
    name: lint
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-go@v5
        with:
          go-version-file: 'go.mod'
      - name: golangci-lint
        uses: golangci/golangci-lint-action@v8
        with:
          version: v2.1.6
          args: -v
