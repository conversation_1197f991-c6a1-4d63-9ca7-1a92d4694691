name: sync release

on:
  pull_request:
    types:
      - closed
    branches:
      - "release/*"

jobs:
  sync:
    env:
      GH_TOKEN: ${{ secrets.PAT }}
    if: "${{ github.event.pull_request.merged && startsWith(github.event.pull_request.title, 'chore: release ')}}"
    runs-on: ubuntu-latest

    steps:
      - name: checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: parse commit
        run: |
          MERGE_COMMIT_SHA=$(gh pr view ${{ github.event.pull_request.number }} --json mergeCommit -q .mergeCommit.oid)
          echo "MERGE_COMMIT_SHA=$MERGE_COMMIT_SHA" >> "$GITHUB_ENV"

      - name: parse release tag
        run: |
          while [ -n $(git describe --contains $MERGE_COMMIT_SHA && echo "ok" || echo "") ]; do
            echo "waiting for release...";
            sleep 2
            git fetch --tags
          done;
          RELEASE_TAG=$(git describe --contains $MERGE_COMMIT_SHA)
          echo "RELEASE_TAG=$RELEASE_TAG" >> "$GITHUB_ENV"

      - name: create branch
        run: |
          PR_BRANCH="sync-release-${MERGE_COMMIT_SHA}"
          echo "PR_BRANCH=$PR_BRANCH" >> "$GITHUB_ENV"
          git checkout $MERGE_COMMIT_SHA -b $PR_BRANCH
          git push origin $PR_BRANCH

      - name: create pull request for major or minor release
        if: ${{ endsWith(env.RELEASE_TAG, '.0') }}
        run: |
          COMMIT_OVERRIDE=$(git rev-list --reverse --pretty="%s" --cherry-pick --right-only ${PR_BRANCH}...origin/master | grep -v "commit" | grep -v "chore: sync #" || echo "")

          echo "# Description" >> body
          echo "" >> body
          echo "Syncing release ${RELEASE_TAG} to main branch" >> body
          echo "" >> body
          echo "**WARNING: Do NOT rewrite git history and ALWAYS use a \"Merge Commit\" for merging!**" >> body
          echo "" >> body
          echo "**↓↓ Please review and edit commit overrides before merging ↓↓**" >> body
          echo "" >> body
          echo "BEGIN_COMMIT_OVERRIDE" >> body
          echo "${COMMIT_OVERRIDE}" >> body
          echo "END_COMMIT_OVERRIDE" >> body

          gh pr create \
          --title "chore: sync release ${RELEASE_TAG} to main branch" \
          --body "$(cat body)" \
          --base master \
          --head $PR_BRANCH \
          --assignee '${{ github.event.pull_request.merged_by.login }}'

      - name: create pull request for patch release
        if: ${{ ! endsWith(env.RELEASE_TAG, '.0') }}
        run: |
          COMMIT_OVERRIDE=$(git rev-list --reverse --pretty="%s" --cherry-pick --right-only origin/master...${PR_BRANCH} | grep -v "commit" | grep -v "chore: release" || echo "")

          echo "# Description" >> body
          echo "" >> body
          echo "Syncing patch release ${RELEASE_TAG} to main branch" >> body
          echo "" >> body
          echo "**↓↓ Please review and edit commit overrides before merging ↓↓**" >> body
          echo "" >> body
          echo "BEGIN_COMMIT_OVERRIDE" >> body
          echo "${COMMIT_OVERRIDE}" >> body
          echo "END_COMMIT_OVERRIDE" >> body

          gh pr create \
          --title "chore: sync release ${RELEASE_TAG} to main branch" \
          --body "$(cat body)" \
          --base master \
          --head $PR_BRANCH \
          --assignee '${{ github.event.pull_request.merged_by.login }}'
