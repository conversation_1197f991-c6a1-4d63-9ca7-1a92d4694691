POSTGRES_USER=rudder
POSTGRES_PASSWORD=password
POSTGRES_DB=jobsdb

DEST_TRANSFORM_URL=http://d-transformer:9090
TEST_SINK_URL=http://test-sink:8181

JOBS_DB_HOST=db
JOBS_DB_USER=rudder
JOBS_DB_PORT=5432
JOBS_DB_DB_NAME=jobsdb
JOBS_DB_PASSWORD=password
JOBS_DB_SSL_MODE=disable

CONFIG_BACKEND_URL=https://api.rudderstack.com
CONFIG_BACKEND_TOKEN=<this is deprecating soon use WORKSPACE_TOKEN instead>
WORKSPACE_TOKEN=<your_token_here>
CONFIG_PATH=/app/config/config.yaml

GO_ENV=production

LOG_LEVEL=INFO
INSTANCE_ID=local_dev

STATSD_SERVER_URL=grafana:8125

WAREHOUSE_STAGING_BUCKET_FOLDER_NAME=rudder-warehouse-staging-logs
WAREHOUSE_BUCKET_LOAD_OBJECTS_FOLDER_NAME=rudder-warehouse-load-objects
DESTINATION_BUCKET_FOLDER_NAME=rudder-logs

# Uncomment the following for loading workspace config from a file
# RSERVER_BACKEND_CONFIG_CONFIG_FROM_FILE=true
RSERVER_BACKEND_CONFIG_CONFIG_JSONPATH=/etc/rudderstack/workspaceConfig.json

# Alerting Pagerduty config
ALERT_PROVIDER=pagerduty
PG_ROUTING_KEY=<your_integration/routing_key>

# Alerting VictorOps Config
#ALERT_PROVIDER=victorops
#VICTOROPS_ROUTING_KEY=<your_victorops_routing_key>

# To capture table dumps in AWS S3, uncomment and add AWS IAM keys

# JOBS_BACKUP_STORAGE_PROVIDER=S3
# JOBS_BACKUP_BUCKET=<your_s3_bucket>
# JOBS_BACKUP_PREFIX=<prefix>
# AWS_ACCESS_KEY_ID=
# AWS_SECRET_ACCESS_KEY=

# To capture table dumps in Azure, uncomment and add Azure storage account along with corresponding Azure credentials

# JOBS_BACKUP_STORAGE_PROVIDER=AZURE_BLOB
# JOBS_BACKUP_BUCKET=<your_azure_container>
# JOBS_BACKUP_PREFIX=<prefix>
# AZURE_STORAGE_ACCOUNT=
# AZURE_STORAGE_ACCESS_KEY=

# To capture table dumps in GCS, uncomment and add Google Cloud Storage credentials file path

# JOBS_BACKUP_STORAGE_PROVIDER=GCS
# JOBS_BACKUP_BUCKET=<your_gcs_bucket>
# JOBS_BACKUP_PREFIX=<prefix>
# GOOGLE_APPLICATION_CREDENTIALS=/path/to/credentials

# To capture table dumps in MINIO, uncomment and add MINIO Config keys

# JOBS_BACKUP_STORAGE_PROVIDER=MINIO
# JOBS_BACKUP_BUCKET=<your_minio_bucket>
# JOBS_BACKUP_PREFIX=<prefix>
# MINIO_ENDPOINT=minio:9000
# MINIO_ACCESS_KEY_ID=
# MINIO_SECRET_ACCESS_KEY=
# MINIO_SSL=

# To capture table dumps in Spaces bucket, uncomment and add Spaces Config keys

# JOBS_BACKUP_STORAGE_PROVIDER=DIGITAL_OCEAN_SPACES
# JOBS_BACKUP_BUCKET=<your_spaces_bucket>
# JOBS_BACKUP_PREFIX=<prefix>
# DO_SPACES_ENDPOINT=
# DO_SPACES_ACCESS_KEY_ID=
# DO_SPACES_SECRET_ACCESS_KEY=

# Destination connection testing
RUDDER_CONNECTION_TESTING_BUCKET_FOLDER_NAME=rudder-test-payload

# The below keys are used to enable 2-way ssl for Kafka. If you want to enable the same, use these keys.
# If these keys are used, please make sure the valid key and certificate are there in the corresponding value.
# KAFKA_SSL_CERTIFICATE_FILE_PATH=<certificate_file_path>
# KAFKA_SSL_KEY_FILE_PATH=<key_file_path>

# The below keys are used to give access to upload, download from s3 bucket and also used by copy command for REDSHIFT
# Use this if you don't to not give aws cred in control plane ex: S3, REDSHIFT
# RUDDER_AWS_S3_COPY_USER_ACCESS_KEY_ID=<rudder user access key>
# RUDDER_AWS_S3_COPY_USER_ACCESS_KEY=<rudder user access key secret>
