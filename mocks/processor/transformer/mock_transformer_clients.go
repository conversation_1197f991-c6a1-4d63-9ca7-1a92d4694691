// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/rudderlabs/rudder-server/processor/transformer (interfaces: TransformerClients)
//
// Generated by this command:
//
//	mockgen -destination=../../mocks/processor/transformer/mock_transformer_clients.go -package=mocks_transformer_clients github.com/rudderlabs/rudder-server/processor/transformer TransformerClients
//

// Package mocks_transformer_clients is a generated GoMock package.
package mocks_transformer_clients

import (
	reflect "reflect"

	transformer "github.com/rudderlabs/rudder-server/processor/transformer"
	gomock "go.uber.org/mock/gomock"
)

// MockTransformerClients is a mock of TransformerClients interface.
type MockTransformerClients struct {
	ctrl     *gomock.Controller
	recorder *MockTransformerClientsMockRecorder
	isgomock struct{}
}

// MockTransformerClientsMockRecorder is the mock recorder for MockTransformerClients.
type MockTransformerClientsMockRecorder struct {
	mock *MockTransformerClients
}

// NewMockTransformerClients creates a new mock instance.
func NewMockTransformerClients(ctrl *gomock.Controller) *MockTransformerClients {
	mock := &MockTransformerClients{ctrl: ctrl}
	mock.recorder = &MockTransformerClientsMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTransformerClients) EXPECT() *MockTransformerClientsMockRecorder {
	return m.recorder
}

// Destination mocks base method.
func (m *MockTransformerClients) Destination() transformer.DestinationClient {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Destination")
	ret0, _ := ret[0].(transformer.DestinationClient)
	return ret0
}

// Destination indicates an expected call of Destination.
func (mr *MockTransformerClientsMockRecorder) Destination() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Destination", reflect.TypeOf((*MockTransformerClients)(nil).Destination))
}

// TrackingPlan mocks base method.
func (m *MockTransformerClients) TrackingPlan() transformer.TrackingPlanClient {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TrackingPlan")
	ret0, _ := ret[0].(transformer.TrackingPlanClient)
	return ret0
}

// TrackingPlan indicates an expected call of TrackingPlan.
func (mr *MockTransformerClientsMockRecorder) TrackingPlan() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TrackingPlan", reflect.TypeOf((*MockTransformerClients)(nil).TrackingPlan))
}

// User mocks base method.
func (m *MockTransformerClients) User() transformer.UserClient {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "User")
	ret0, _ := ret[0].(transformer.UserClient)
	return ret0
}

// User indicates an expected call of User.
func (mr *MockTransformerClientsMockRecorder) User() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "User", reflect.TypeOf((*MockTransformerClients)(nil).User))
}

// UserMirror mocks base method.
func (m *MockTransformerClients) UserMirror() transformer.UserClient {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UserMirror")
	ret0, _ := ret[0].(transformer.UserClient)
	return ret0
}

// UserMirror indicates an expected call of UserMirror.
func (mr *MockTransformerClientsMockRecorder) UserMirror() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UserMirror", reflect.TypeOf((*MockTransformerClients)(nil).UserMirror))
}
