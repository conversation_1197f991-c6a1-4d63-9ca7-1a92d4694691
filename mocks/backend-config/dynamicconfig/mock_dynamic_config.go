// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/rudderlabs/rudder-server/backend-config/dynamicconfig (interfaces: Cache)
//
// Generated by this command:
//
//	mockgen -destination=./mocks/backend-config/dynamicconfig/mock_dynamic_config.go -package=mock_dynamicconfig github.com/rudderlabs/rudder-server/backend-config/dynamicconfig Cache
//

// Package mock_dynamicconfig is a generated GoMock package.
package mock_dynamicconfig

import (
	reflect "reflect"

	dynamicconfig "github.com/rudderlabs/rudder-server/backend-config/dynamicconfig"
	gomock "go.uber.org/mock/gomock"
)

// MockCache is a mock of Cache interface.
type MockCache struct {
	ctrl     *gomock.Controller
	recorder *MockCacheMockRecorder
	isgomock struct{}
}

// MockCacheMockRecorder is the mock recorder for MockCache.
type MockCacheMockRecorder struct {
	mock *MockCache
}

// NewMockCache creates a new mock instance.
func NewMockCache(ctrl *gomock.Controller) *MockCache {
	mock := &MockCache{ctrl: ctrl}
	mock.recorder = &MockCacheMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCache) EXPECT() *MockCacheMockRecorder {
	return m.recorder
}

// Get mocks base method.
func (m *MockCache) Get(destID string) (*dynamicconfig.DestinationRevisionInfo, bool) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Get", destID)
	ret0, _ := ret[0].(*dynamicconfig.DestinationRevisionInfo)
	ret1, _ := ret[1].(bool)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockCacheMockRecorder) Get(destID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockCache)(nil).Get), destID)
}

// Len mocks base method.
func (m *MockCache) Len() int {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Len")
	ret0, _ := ret[0].(int)
	return ret0
}

// Len indicates an expected call of Len.
func (mr *MockCacheMockRecorder) Len() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Len", reflect.TypeOf((*MockCache)(nil).Len))
}

// Set mocks base method.
func (m *MockCache) Set(destID string, info *dynamicconfig.DestinationRevisionInfo) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Set", destID, info)
}

// Set indicates an expected call of Set.
func (mr *MockCacheMockRecorder) Set(destID, info any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Set", reflect.TypeOf((*MockCache)(nil).Set), destID, info)
}
