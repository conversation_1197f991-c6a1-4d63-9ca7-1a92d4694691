// Code generated by MockGen. DO NOT EDIT.
// Source: ./aggregator.go
//
// Generated by this command:
//
//	mockgen -destination=./aggregator_mock.go -package=aggregator -source=./aggregator.go Aggregator
//

// Package aggregator is a generated GoMock package.
package aggregator

import (
	context "context"
	json "encoding/json"
	reflect "reflect"
	time "time"

	gomock "go.uber.org/mock/gomock"
)

// MockAggregator is a mock of Aggregator interface.
type MockAggregator struct {
	ctrl     *gomock.Controller
	recorder *MockAggregatorMockRecorder
	isgomock struct{}
}

// MockAggregatorMockRecorder is the mock recorder for MockAggregator.
type MockAggregatorMockRecorder struct {
	mock *MockAggregator
}

// NewMockAggregator creates a new mock instance.
func NewMockAggregator(ctrl *gomock.Controller) *MockAggregator {
	mock := &MockAggregator{ctrl: ctrl}
	mock.recorder = &MockAggregatorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAggregator) EXPECT() *MockAggregatorMockRecorder {
	return m.recorder
}

// Aggregate mocks base method.
func (m *MockAggregator) Aggregate(ctx context.Context, start, end time.Time) ([]json.RawMessage, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Aggregate", ctx, start, end)
	ret0, _ := ret[0].([]json.RawMessage)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Aggregate indicates an expected call of Aggregate.
func (mr *MockAggregatorMockRecorder) Aggregate(ctx, start, end any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Aggregate", reflect.TypeOf((*MockAggregator)(nil).Aggregate), ctx, start, end)
}
