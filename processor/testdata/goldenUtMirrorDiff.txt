elements differ

extra elements in list A:
([]interface {}) (len=3) {
 (types.TransformerResponse) {
  Output: (map[string]interface {}) (len=11) {
   (string) (len=7) "context": (map[string]interface {}) {
   },
   (string) (len=12) "integrations": (map[string]interface {}) (len=2) {
    (string) (len=3) "All": (bool) false,
    (string) (len=45) "enabled-destination-b-definition-display-name": (bool) true
   },
   (string) (len=9) "messageId": (string) (len=9) "message-1",
   (string) (len=17) "originalTimestamp": (string) (len=24) "2000-01-02T01:23:45.000Z",
   (string) (len=10) "receivedAt": (string) (len=24) "2001-01-02T02:23:45.000Z",
   (string) (len=10) "request_ip": (string) (len=7) "*******",
   (string) (len=8) "rudderId": (string) (len=14) "some-rudder-id",
   (string) (len=6) "sentAt": (string) (len=24) "2000-01-02T01:23:00.000Z",
   (string) (len=13) "some-property": (string) (len=10) "property-1",
   (string) (len=9) "timestamp": (string) (len=24) "2001-01-02T02:24:30.000Z",
   (string) (len=14) "user-transform": (string) (len=5) "value"
  },
  Metadata: (types.Metadata) {
   SourceID: (string) (len=22) "enabled-source-only-ut",
   SourceName: (string) (len=21) "SourceIDEnabledOnlyUT",
   OriginalSourceID: (string) "",
   WorkspaceID: (string) "",
   Namespace: (string) "",
   InstanceID: (string) "",
   SourceType: (string) "",
   SourceCategory: (string) "",
   TrackingPlanID: (string) "",
   TrackingPlanVersion: (int) 0,
   SourceTpConfig: (map[string]map[string]interface {}) <nil>,
   MergedTpConfig: (map[string]interface {}) <nil>,
   DestinationID: (string) (len=21) "enabled-destination-b",
   JobID: (int64) 0,
   SourceJobID: (string) "",
   SourceJobRunID: (string) "",
   SourceTaskRunID: (string) "",
   RecordID: (interface {}) <nil>,
   DestinationType: (string) (len=5) "MINIO",
   DestinationName: (string) "",
   MessageID: (string) "",
   OAuthAccessToken: (string) "",
   TraceParent: (string) "",
   MessageIDs: ([]string) <nil>,
   RudderID: (string) "",
   ReceivedAt: (string) "",
   EventName: (string) "",
   EventType: (string) "",
   SourceDefinitionID: (string) "",
   DestinationDefinitionID: (string) "",
   TransformationID: (string) "",
   TransformationVersionID: (string) "",
   SourceDefinitionType: (string) ""
  },
  StatusCode: (int) 200,
  Error: (string) "",
  ValidationErrors: ([]types.ValidationError) <nil>,
  StatTags: (map[string]string) <nil>
 },
 (types.TransformerResponse) {
  Output: (map[string]interface {}) (len=11) {
   (string) (len=7) "context": (map[string]interface {}) {
   },
   (string) (len=12) "integrations": (map[string]interface {}) (len=1) {
    (string) (len=3) "All": (bool) true
   },
   (string) (len=9) "messageId": (string) (len=9) "message-3",
   (string) (len=17) "originalTimestamp": (string) (len=24) "2002-01-02T02:23:45.000Z",
   (string) (len=10) "receivedAt": (string) (len=24) "2002-01-02T02:23:45.000Z",
   (string) (len=10) "request_ip": (string) (len=7) "*******",
   (string) (len=8) "rudderId": (string) (len=14) "some-rudder-id",
   (string) (len=6) "sentAt": (string) (len=24) "2000-03-02T01:23:15.000Z",
   (string) (len=13) "some-property": (string) (len=10) "property-3",
   (string) (len=9) "timestamp": (string) (len=24) "2003-11-04T03:24:15.000Z",
   (string) (len=14) "user-transform": (string) (len=5) "value"
  },
  Metadata: (types.Metadata) {
   SourceID: (string) (len=22) "enabled-source-only-ut",
   SourceName: (string) (len=21) "SourceIDEnabledOnlyUT",
   OriginalSourceID: (string) "",
   WorkspaceID: (string) "",
   Namespace: (string) "",
   InstanceID: (string) "",
   SourceType: (string) "",
   SourceCategory: (string) "",
   TrackingPlanID: (string) "",
   TrackingPlanVersion: (int) 0,
   SourceTpConfig: (map[string]map[string]interface {}) <nil>,
   MergedTpConfig: (map[string]interface {}) <nil>,
   DestinationID: (string) (len=21) "enabled-destination-b",
   JobID: (int64) 0,
   SourceJobID: (string) "",
   SourceJobRunID: (string) "",
   SourceTaskRunID: (string) "",
   RecordID: (interface {}) <nil>,
   DestinationType: (string) (len=5) "MINIO",
   DestinationName: (string) "",
   MessageID: (string) "",
   OAuthAccessToken: (string) "",
   TraceParent: (string) "",
   MessageIDs: ([]string) <nil>,
   RudderID: (string) "",
   ReceivedAt: (string) "",
   EventName: (string) "",
   EventType: (string) "",
   SourceDefinitionID: (string) "",
   DestinationDefinitionID: (string) "",
   TransformationID: (string) "",
   TransformationVersionID: (string) "",
   SourceDefinitionType: (string) ""
  },
  StatusCode: (int) 200,
  Error: (string) "",
  ValidationErrors: ([]types.ValidationError) <nil>,
  StatTags: (map[string]string) <nil>
 },
 (types.TransformerResponse) {
  Output: (map[string]interface {}) (len=11) {
   (string) (len=7) "context": (map[string]interface {}) {
   },
   (string) (len=12) "integrations": (map[string]interface {}) {
   },
   (string) (len=9) "messageId": (string) (len=9) "message-4",
   (string) (len=17) "originalTimestamp": (string) (len=24) "2000-04-02T02:23:15.000Z",
   (string) (len=10) "receivedAt": (string) (len=24) "2002-01-02T02:23:45.000Z",
   (string) (len=10) "request_ip": (string) (len=7) "*******",
   (string) (len=8) "rudderId": (string) (len=14) "some-rudder-id",
   (string) (len=6) "sentAt": (string) (len=24) "2002-01-02T02:23:45.000Z",
   (string) (len=13) "some-property": (string) (len=10) "property-4",
   (string) (len=9) "timestamp": (string) (len=24) "2000-04-02T02:23:15.000Z",
   (string) (len=14) "user-transform": (string) (len=5) "value"
  },
  Metadata: (types.Metadata) {
   SourceID: (string) (len=22) "enabled-source-only-ut",
   SourceName: (string) (len=21) "SourceIDEnabledOnlyUT",
   OriginalSourceID: (string) "",
   WorkspaceID: (string) "",
   Namespace: (string) "",
   InstanceID: (string) "",
   SourceType: (string) "",
   SourceCategory: (string) "",
   TrackingPlanID: (string) "",
   TrackingPlanVersion: (int) 0,
   SourceTpConfig: (map[string]map[string]interface {}) <nil>,
   MergedTpConfig: (map[string]interface {}) <nil>,
   DestinationID: (string) (len=21) "enabled-destination-b",
   JobID: (int64) 0,
   SourceJobID: (string) "",
   SourceJobRunID: (string) "",
   SourceTaskRunID: (string) "",
   RecordID: (interface {}) <nil>,
   DestinationType: (string) (len=5) "MINIO",
   DestinationName: (string) "",
   MessageID: (string) "",
   OAuthAccessToken: (string) "",
   TraceParent: (string) "",
   MessageIDs: ([]string) <nil>,
   RudderID: (string) "",
   ReceivedAt: (string) "",
   EventName: (string) "",
   EventType: (string) "",
   SourceDefinitionID: (string) "",
   DestinationDefinitionID: (string) "",
   TransformationID: (string) "",
   TransformationVersionID: (string) "",
   SourceDefinitionType: (string) ""
  },
  StatusCode: (int) 200,
  Error: (string) "",
  ValidationErrors: ([]types.ValidationError) <nil>,
  StatTags: (map[string]string) <nil>
 }
}


extra elements in list B:
([]interface {}) (len=3) {
 (types.TransformerResponse) {
  Output: (map[string]interface {}) (len=11) {
   (string) (len=7) "context": (map[string]interface {}) {
   },
   (string) (len=12) "integrations": (map[string]interface {}) (len=2) {
    (string) (len=3) "All": (bool) false,
    (string) (len=45) "enabled-destination-b-definition-display-name": (bool) true
   },
   (string) (len=9) "messageId": (string) (len=9) "message-1",
   (string) (len=17) "originalTimestamp": (string) (len=24) "2000-01-02T01:23:45.000Z",
   (string) (len=10) "receivedAt": (string) (len=24) "2001-01-02T02:23:45.000Z",
   (string) (len=10) "request_ip": (string) (len=7) "*******",
   (string) (len=8) "rudderId": (string) (len=14) "some-rudder-id",
   (string) (len=6) "sentAt": (string) (len=24) "2000-01-02T01:23:00.000Z",
   (string) (len=13) "some-property": (string) (len=10) "property-1",
   (string) (len=9) "timestamp": (string) (len=24) "2001-01-02T02:24:30.000Z",
   (string) (len=14) "user-transform": (string) (len=12) "value-mirror"
  },
  Metadata: (types.Metadata) {
   SourceID: (string) (len=22) "enabled-source-only-ut",
   SourceName: (string) (len=21) "SourceIDEnabledOnlyUT",
   OriginalSourceID: (string) "",
   WorkspaceID: (string) "",
   Namespace: (string) "",
   InstanceID: (string) "",
   SourceType: (string) "",
   SourceCategory: (string) "",
   TrackingPlanID: (string) "",
   TrackingPlanVersion: (int) 0,
   SourceTpConfig: (map[string]map[string]interface {}) <nil>,
   MergedTpConfig: (map[string]interface {}) <nil>,
   DestinationID: (string) (len=21) "enabled-destination-b",
   JobID: (int64) 0,
   SourceJobID: (string) "",
   SourceJobRunID: (string) "",
   SourceTaskRunID: (string) "",
   RecordID: (interface {}) <nil>,
   DestinationType: (string) (len=5) "MINIO",
   DestinationName: (string) "",
   MessageID: (string) "",
   OAuthAccessToken: (string) "",
   TraceParent: (string) "",
   MessageIDs: ([]string) <nil>,
   RudderID: (string) "",
   ReceivedAt: (string) "",
   EventName: (string) "",
   EventType: (string) "",
   SourceDefinitionID: (string) "",
   DestinationDefinitionID: (string) "",
   TransformationID: (string) "",
   TransformationVersionID: (string) "",
   SourceDefinitionType: (string) ""
  },
  StatusCode: (int) 200,
  Error: (string) "",
  ValidationErrors: ([]types.ValidationError) <nil>,
  StatTags: (map[string]string) <nil>
 },
 (types.TransformerResponse) {
  Output: (map[string]interface {}) (len=11) {
   (string) (len=7) "context": (map[string]interface {}) {
   },
   (string) (len=12) "integrations": (map[string]interface {}) (len=1) {
    (string) (len=3) "All": (bool) true
   },
   (string) (len=9) "messageId": (string) (len=9) "message-3",
   (string) (len=17) "originalTimestamp": (string) (len=24) "2002-01-02T02:23:45.000Z",
   (string) (len=10) "receivedAt": (string) (len=24) "2002-01-02T02:23:45.000Z",
   (string) (len=10) "request_ip": (string) (len=7) "*******",
   (string) (len=8) "rudderId": (string) (len=14) "some-rudder-id",
   (string) (len=6) "sentAt": (string) (len=24) "2000-03-02T01:23:15.000Z",
   (string) (len=13) "some-property": (string) (len=10) "property-3",
   (string) (len=9) "timestamp": (string) (len=24) "2003-11-04T03:24:15.000Z",
   (string) (len=14) "user-transform": (string) (len=12) "value-mirror"
  },
  Metadata: (types.Metadata) {
   SourceID: (string) (len=22) "enabled-source-only-ut",
   SourceName: (string) (len=21) "SourceIDEnabledOnlyUT",
   OriginalSourceID: (string) "",
   WorkspaceID: (string) "",
   Namespace: (string) "",
   InstanceID: (string) "",
   SourceType: (string) "",
   SourceCategory: (string) "",
   TrackingPlanID: (string) "",
   TrackingPlanVersion: (int) 0,
   SourceTpConfig: (map[string]map[string]interface {}) <nil>,
   MergedTpConfig: (map[string]interface {}) <nil>,
   DestinationID: (string) (len=21) "enabled-destination-b",
   JobID: (int64) 0,
   SourceJobID: (string) "",
   SourceJobRunID: (string) "",
   SourceTaskRunID: (string) "",
   RecordID: (interface {}) <nil>,
   DestinationType: (string) (len=5) "MINIO",
   DestinationName: (string) "",
   MessageID: (string) "",
   OAuthAccessToken: (string) "",
   TraceParent: (string) "",
   MessageIDs: ([]string) <nil>,
   RudderID: (string) "",
   ReceivedAt: (string) "",
   EventName: (string) "",
   EventType: (string) "",
   SourceDefinitionID: (string) "",
   DestinationDefinitionID: (string) "",
   TransformationID: (string) "",
   TransformationVersionID: (string) "",
   SourceDefinitionType: (string) ""
  },
  StatusCode: (int) 200,
  Error: (string) "",
  ValidationErrors: ([]types.ValidationError) <nil>,
  StatTags: (map[string]string) <nil>
 },
 (types.TransformerResponse) {
  Output: (map[string]interface {}) (len=11) {
   (string) (len=7) "context": (map[string]interface {}) {
   },
   (string) (len=12) "integrations": (map[string]interface {}) {
   },
   (string) (len=9) "messageId": (string) (len=9) "message-4",
   (string) (len=17) "originalTimestamp": (string) (len=24) "2000-04-02T02:23:15.000Z",
   (string) (len=10) "receivedAt": (string) (len=24) "2002-01-02T02:23:45.000Z",
   (string) (len=10) "request_ip": (string) (len=7) "*******",
   (string) (len=8) "rudderId": (string) (len=14) "some-rudder-id",
   (string) (len=6) "sentAt": (string) (len=24) "2002-01-02T02:23:45.000Z",
   (string) (len=13) "some-property": (string) (len=10) "property-4",
   (string) (len=9) "timestamp": (string) (len=24) "2000-04-02T02:23:15.000Z",
   (string) (len=14) "user-transform": (string) (len=12) "value-mirror"
  },
  Metadata: (types.Metadata) {
   SourceID: (string) (len=22) "enabled-source-only-ut",
   SourceName: (string) (len=21) "SourceIDEnabledOnlyUT",
   OriginalSourceID: (string) "",
   WorkspaceID: (string) "",
   Namespace: (string) "",
   InstanceID: (string) "",
   SourceType: (string) "",
   SourceCategory: (string) "",
   TrackingPlanID: (string) "",
   TrackingPlanVersion: (int) 0,
   SourceTpConfig: (map[string]map[string]interface {}) <nil>,
   MergedTpConfig: (map[string]interface {}) <nil>,
   DestinationID: (string) (len=21) "enabled-destination-b",
   JobID: (int64) 0,
   SourceJobID: (string) "",
   SourceJobRunID: (string) "",
   SourceTaskRunID: (string) "",
   RecordID: (interface {}) <nil>,
   DestinationType: (string) (len=5) "MINIO",
   DestinationName: (string) "",
   MessageID: (string) "",
   OAuthAccessToken: (string) "",
   TraceParent: (string) "",
   MessageIDs: ([]string) <nil>,
   RudderID: (string) "",
   ReceivedAt: (string) "",
   EventName: (string) "",
   EventType: (string) "",
   SourceDefinitionID: (string) "",
   DestinationDefinitionID: (string) "",
   TransformationID: (string) "",
   TransformationVersionID: (string) "",
   SourceDefinitionType: (string) ""
  },
  StatusCode: (int) 200,
  Error: (string) "",
  ValidationErrors: ([]types.ValidationError) <nil>,
  StatTags: (map[string]string) <nil>
 }
}


listA:
([]types.TransformerResponse) (len=3) {
 (types.TransformerResponse) {
  Output: (map[string]interface {}) (len=11) {
   (string) (len=7) "context": (map[string]interface {}) {
   },
   (string) (len=12) "integrations": (map[string]interface {}) (len=2) {
    (string) (len=3) "All": (bool) false,
    (string) (len=45) "enabled-destination-b-definition-display-name": (bool) true
   },
   (string) (len=9) "messageId": (string) (len=9) "message-1",
   (string) (len=17) "originalTimestamp": (string) (len=24) "2000-01-02T01:23:45.000Z",
   (string) (len=10) "receivedAt": (string) (len=24) "2001-01-02T02:23:45.000Z",
   (string) (len=10) "request_ip": (string) (len=7) "*******",
   (string) (len=8) "rudderId": (string) (len=14) "some-rudder-id",
   (string) (len=6) "sentAt": (string) (len=24) "2000-01-02T01:23:00.000Z",
   (string) (len=13) "some-property": (string) (len=10) "property-1",
   (string) (len=9) "timestamp": (string) (len=24) "2001-01-02T02:24:30.000Z",
   (string) (len=14) "user-transform": (string) (len=5) "value"
  },
  Metadata: (types.Metadata) {
   SourceID: (string) (len=22) "enabled-source-only-ut",
   SourceName: (string) (len=21) "SourceIDEnabledOnlyUT",
   OriginalSourceID: (string) "",
   WorkspaceID: (string) "",
   Namespace: (string) "",
   InstanceID: (string) "",
   SourceType: (string) "",
   SourceCategory: (string) "",
   TrackingPlanID: (string) "",
   TrackingPlanVersion: (int) 0,
   SourceTpConfig: (map[string]map[string]interface {}) <nil>,
   MergedTpConfig: (map[string]interface {}) <nil>,
   DestinationID: (string) (len=21) "enabled-destination-b",
   JobID: (int64) 0,
   SourceJobID: (string) "",
   SourceJobRunID: (string) "",
   SourceTaskRunID: (string) "",
   RecordID: (interface {}) <nil>,
   DestinationType: (string) (len=5) "MINIO",
   DestinationName: (string) "",
   MessageID: (string) "",
   OAuthAccessToken: (string) "",
   TraceParent: (string) "",
   MessageIDs: ([]string) <nil>,
   RudderID: (string) "",
   ReceivedAt: (string) "",
   EventName: (string) "",
   EventType: (string) "",
   SourceDefinitionID: (string) "",
   DestinationDefinitionID: (string) "",
   TransformationID: (string) "",
   TransformationVersionID: (string) "",
   SourceDefinitionType: (string) ""
  },
  StatusCode: (int) 200,
  Error: (string) "",
  ValidationErrors: ([]types.ValidationError) <nil>,
  StatTags: (map[string]string) <nil>
 },
 (types.TransformerResponse) {
  Output: (map[string]interface {}) (len=11) {
   (string) (len=7) "context": (map[string]interface {}) {
   },
   (string) (len=12) "integrations": (map[string]interface {}) (len=1) {
    (string) (len=3) "All": (bool) true
   },
   (string) (len=9) "messageId": (string) (len=9) "message-3",
   (string) (len=17) "originalTimestamp": (string) (len=24) "2002-01-02T02:23:45.000Z",
   (string) (len=10) "receivedAt": (string) (len=24) "2002-01-02T02:23:45.000Z",
   (string) (len=10) "request_ip": (string) (len=7) "*******",
   (string) (len=8) "rudderId": (string) (len=14) "some-rudder-id",
   (string) (len=6) "sentAt": (string) (len=24) "2000-03-02T01:23:15.000Z",
   (string) (len=13) "some-property": (string) (len=10) "property-3",
   (string) (len=9) "timestamp": (string) (len=24) "2003-11-04T03:24:15.000Z",
   (string) (len=14) "user-transform": (string) (len=5) "value"
  },
  Metadata: (types.Metadata) {
   SourceID: (string) (len=22) "enabled-source-only-ut",
   SourceName: (string) (len=21) "SourceIDEnabledOnlyUT",
   OriginalSourceID: (string) "",
   WorkspaceID: (string) "",
   Namespace: (string) "",
   InstanceID: (string) "",
   SourceType: (string) "",
   SourceCategory: (string) "",
   TrackingPlanID: (string) "",
   TrackingPlanVersion: (int) 0,
   SourceTpConfig: (map[string]map[string]interface {}) <nil>,
   MergedTpConfig: (map[string]interface {}) <nil>,
   DestinationID: (string) (len=21) "enabled-destination-b",
   JobID: (int64) 0,
   SourceJobID: (string) "",
   SourceJobRunID: (string) "",
   SourceTaskRunID: (string) "",
   RecordID: (interface {}) <nil>,
   DestinationType: (string) (len=5) "MINIO",
   DestinationName: (string) "",
   MessageID: (string) "",
   OAuthAccessToken: (string) "",
   TraceParent: (string) "",
   MessageIDs: ([]string) <nil>,
   RudderID: (string) "",
   ReceivedAt: (string) "",
   EventName: (string) "",
   EventType: (string) "",
   SourceDefinitionID: (string) "",
   DestinationDefinitionID: (string) "",
   TransformationID: (string) "",
   TransformationVersionID: (string) "",
   SourceDefinitionType: (string) ""
  },
  StatusCode: (int) 200,
  Error: (string) "",
  ValidationErrors: ([]types.ValidationError) <nil>,
  StatTags: (map[string]string) <nil>
 },
 (types.TransformerResponse) {
  Output: (map[string]interface {}) (len=11) {
   (string) (len=7) "context": (map[string]interface {}) {
   },
   (string) (len=12) "integrations": (map[string]interface {}) {
   },
   (string) (len=9) "messageId": (string) (len=9) "message-4",
   (string) (len=17) "originalTimestamp": (string) (len=24) "2000-04-02T02:23:15.000Z",
   (string) (len=10) "receivedAt": (string) (len=24) "2002-01-02T02:23:45.000Z",
   (string) (len=10) "request_ip": (string) (len=7) "*******",
   (string) (len=8) "rudderId": (string) (len=14) "some-rudder-id",
   (string) (len=6) "sentAt": (string) (len=24) "2002-01-02T02:23:45.000Z",
   (string) (len=13) "some-property": (string) (len=10) "property-4",
   (string) (len=9) "timestamp": (string) (len=24) "2000-04-02T02:23:15.000Z",
   (string) (len=14) "user-transform": (string) (len=5) "value"
  },
  Metadata: (types.Metadata) {
   SourceID: (string) (len=22) "enabled-source-only-ut",
   SourceName: (string) (len=21) "SourceIDEnabledOnlyUT",
   OriginalSourceID: (string) "",
   WorkspaceID: (string) "",
   Namespace: (string) "",
   InstanceID: (string) "",
   SourceType: (string) "",
   SourceCategory: (string) "",
   TrackingPlanID: (string) "",
   TrackingPlanVersion: (int) 0,
   SourceTpConfig: (map[string]map[string]interface {}) <nil>,
   MergedTpConfig: (map[string]interface {}) <nil>,
   DestinationID: (string) (len=21) "enabled-destination-b",
   JobID: (int64) 0,
   SourceJobID: (string) "",
   SourceJobRunID: (string) "",
   SourceTaskRunID: (string) "",
   RecordID: (interface {}) <nil>,
   DestinationType: (string) (len=5) "MINIO",
   DestinationName: (string) "",
   MessageID: (string) "",
   OAuthAccessToken: (string) "",
   TraceParent: (string) "",
   MessageIDs: ([]string) <nil>,
   RudderID: (string) "",
   ReceivedAt: (string) "",
   EventName: (string) "",
   EventType: (string) "",
   SourceDefinitionID: (string) "",
   DestinationDefinitionID: (string) "",
   TransformationID: (string) "",
   TransformationVersionID: (string) "",
   SourceDefinitionType: (string) ""
  },
  StatusCode: (int) 200,
  Error: (string) "",
  ValidationErrors: ([]types.ValidationError) <nil>,
  StatTags: (map[string]string) <nil>
 }
}


listB:
([]types.TransformerResponse) (len=3) {
 (types.TransformerResponse) {
  Output: (map[string]interface {}) (len=11) {
   (string) (len=7) "context": (map[string]interface {}) {
   },
   (string) (len=12) "integrations": (map[string]interface {}) (len=2) {
    (string) (len=3) "All": (bool) false,
    (string) (len=45) "enabled-destination-b-definition-display-name": (bool) true
   },
   (string) (len=9) "messageId": (string) (len=9) "message-1",
   (string) (len=17) "originalTimestamp": (string) (len=24) "2000-01-02T01:23:45.000Z",
   (string) (len=10) "receivedAt": (string) (len=24) "2001-01-02T02:23:45.000Z",
   (string) (len=10) "request_ip": (string) (len=7) "*******",
   (string) (len=8) "rudderId": (string) (len=14) "some-rudder-id",
   (string) (len=6) "sentAt": (string) (len=24) "2000-01-02T01:23:00.000Z",
   (string) (len=13) "some-property": (string) (len=10) "property-1",
   (string) (len=9) "timestamp": (string) (len=24) "2001-01-02T02:24:30.000Z",
   (string) (len=14) "user-transform": (string) (len=12) "value-mirror"
  },
  Metadata: (types.Metadata) {
   SourceID: (string) (len=22) "enabled-source-only-ut",
   SourceName: (string) (len=21) "SourceIDEnabledOnlyUT",
   OriginalSourceID: (string) "",
   WorkspaceID: (string) "",
   Namespace: (string) "",
   InstanceID: (string) "",
   SourceType: (string) "",
   SourceCategory: (string) "",
   TrackingPlanID: (string) "",
   TrackingPlanVersion: (int) 0,
   SourceTpConfig: (map[string]map[string]interface {}) <nil>,
   MergedTpConfig: (map[string]interface {}) <nil>,
   DestinationID: (string) (len=21) "enabled-destination-b",
   JobID: (int64) 0,
   SourceJobID: (string) "",
   SourceJobRunID: (string) "",
   SourceTaskRunID: (string) "",
   RecordID: (interface {}) <nil>,
   DestinationType: (string) (len=5) "MINIO",
   DestinationName: (string) "",
   MessageID: (string) "",
   OAuthAccessToken: (string) "",
   TraceParent: (string) "",
   MessageIDs: ([]string) <nil>,
   RudderID: (string) "",
   ReceivedAt: (string) "",
   EventName: (string) "",
   EventType: (string) "",
   SourceDefinitionID: (string) "",
   DestinationDefinitionID: (string) "",
   TransformationID: (string) "",
   TransformationVersionID: (string) "",
   SourceDefinitionType: (string) ""
  },
  StatusCode: (int) 200,
  Error: (string) "",
  ValidationErrors: ([]types.ValidationError) <nil>,
  StatTags: (map[string]string) <nil>
 },
 (types.TransformerResponse) {
  Output: (map[string]interface {}) (len=11) {
   (string) (len=7) "context": (map[string]interface {}) {
   },
   (string) (len=12) "integrations": (map[string]interface {}) (len=1) {
    (string) (len=3) "All": (bool) true
   },
   (string) (len=9) "messageId": (string) (len=9) "message-3",
   (string) (len=17) "originalTimestamp": (string) (len=24) "2002-01-02T02:23:45.000Z",
   (string) (len=10) "receivedAt": (string) (len=24) "2002-01-02T02:23:45.000Z",
   (string) (len=10) "request_ip": (string) (len=7) "*******",
   (string) (len=8) "rudderId": (string) (len=14) "some-rudder-id",
   (string) (len=6) "sentAt": (string) (len=24) "2000-03-02T01:23:15.000Z",
   (string) (len=13) "some-property": (string) (len=10) "property-3",
   (string) (len=9) "timestamp": (string) (len=24) "2003-11-04T03:24:15.000Z",
   (string) (len=14) "user-transform": (string) (len=12) "value-mirror"
  },
  Metadata: (types.Metadata) {
   SourceID: (string) (len=22) "enabled-source-only-ut",
   SourceName: (string) (len=21) "SourceIDEnabledOnlyUT",
   OriginalSourceID: (string) "",
   WorkspaceID: (string) "",
   Namespace: (string) "",
   InstanceID: (string) "",
   SourceType: (string) "",
   SourceCategory: (string) "",
   TrackingPlanID: (string) "",
   TrackingPlanVersion: (int) 0,
   SourceTpConfig: (map[string]map[string]interface {}) <nil>,
   MergedTpConfig: (map[string]interface {}) <nil>,
   DestinationID: (string) (len=21) "enabled-destination-b",
   JobID: (int64) 0,
   SourceJobID: (string) "",
   SourceJobRunID: (string) "",
   SourceTaskRunID: (string) "",
   RecordID: (interface {}) <nil>,
   DestinationType: (string) (len=5) "MINIO",
   DestinationName: (string) "",
   MessageID: (string) "",
   OAuthAccessToken: (string) "",
   TraceParent: (string) "",
   MessageIDs: ([]string) <nil>,
   RudderID: (string) "",
   ReceivedAt: (string) "",
   EventName: (string) "",
   EventType: (string) "",
   SourceDefinitionID: (string) "",
   DestinationDefinitionID: (string) "",
   TransformationID: (string) "",
   TransformationVersionID: (string) "",
   SourceDefinitionType: (string) ""
  },
  StatusCode: (int) 200,
  Error: (string) "",
  ValidationErrors: ([]types.ValidationError) <nil>,
  StatTags: (map[string]string) <nil>
 },
 (types.TransformerResponse) {
  Output: (map[string]interface {}) (len=11) {
   (string) (len=7) "context": (map[string]interface {}) {
   },
   (string) (len=12) "integrations": (map[string]interface {}) {
   },
   (string) (len=9) "messageId": (string) (len=9) "message-4",
   (string) (len=17) "originalTimestamp": (string) (len=24) "2000-04-02T02:23:15.000Z",
   (string) (len=10) "receivedAt": (string) (len=24) "2002-01-02T02:23:45.000Z",
   (string) (len=10) "request_ip": (string) (len=7) "*******",
   (string) (len=8) "rudderId": (string) (len=14) "some-rudder-id",
   (string) (len=6) "sentAt": (string) (len=24) "2002-01-02T02:23:45.000Z",
   (string) (len=13) "some-property": (string) (len=10) "property-4",
   (string) (len=9) "timestamp": (string) (len=24) "2000-04-02T02:23:15.000Z",
   (string) (len=14) "user-transform": (string) (len=12) "value-mirror"
  },
  Metadata: (types.Metadata) {
   SourceID: (string) (len=22) "enabled-source-only-ut",
   SourceName: (string) (len=21) "SourceIDEnabledOnlyUT",
   OriginalSourceID: (string) "",
   WorkspaceID: (string) "",
   Namespace: (string) "",
   InstanceID: (string) "",
   SourceType: (string) "",
   SourceCategory: (string) "",
   TrackingPlanID: (string) "",
   TrackingPlanVersion: (int) 0,
   SourceTpConfig: (map[string]map[string]interface {}) <nil>,
   MergedTpConfig: (map[string]interface {}) <nil>,
   DestinationID: (string) (len=21) "enabled-destination-b",
   JobID: (int64) 0,
   SourceJobID: (string) "",
   SourceJobRunID: (string) "",
   SourceTaskRunID: (string) "",
   RecordID: (interface {}) <nil>,
   DestinationType: (string) (len=5) "MINIO",
   DestinationName: (string) "",
   MessageID: (string) "",
   OAuthAccessToken: (string) "",
   TraceParent: (string) "",
   MessageIDs: ([]string) <nil>,
   RudderID: (string) "",
   ReceivedAt: (string) "",
   EventName: (string) "",
   EventType: (string) "",
   SourceDefinitionID: (string) "",
   DestinationDefinitionID: (string) "",
   TransformationID: (string) "",
   TransformationVersionID: (string) "",
   SourceDefinitionType: (string) ""
  },
  StatusCode: (int) 200,
  Error: (string) "",
  ValidationErrors: ([]types.ValidationError) <nil>,
  StatTags: (map[string]string) <nil>
 }
}
