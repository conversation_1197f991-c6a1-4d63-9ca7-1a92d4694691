{"AZURE_DATALAKE": ["INTO", "CONSTRAINT", "CURRENT", "READ", "WITHIN", "NO", "ROWGUIDCOL", "WITH", "NCLOB", "REGR_SYY", "SETUSER", "CONDITION", "MAP", "MINUTE", "PARTITION", "SYSTEM_USER", "NULL", "OBJECT", "SEMANTICSIMILARITYDETAILSTABLE", "SESSION", "ELEMENT", "HOUR", "MOD", "PUBLIC", "SEMANTICSIMILARITYTABLE", "EXIT", "LINENO", "SECURITYAUDIT", "TRIM", "VAR_POP", "CORRESPONDING", "CREATE", "IMMEDIATE", "XMLATTRIBUTES", "CLUSTERED", "DENY", "LAST", "OCCURRENCES_REGEX", "REF", "RELATIVE", "SELECT", "XMLEXISTS", "BETWEEN", "FREETEXTTABLE", "PAD", "WITHOUT", "SYSTEM", "WHENEVER", "DEC", "DIAGNOSTICS", "GENERAL", "LARGE", "METHOD", "ATOMIC", "DETERMINISTIC", "OLD", "UNPIVOT", "USING", "DESCRIPTOR", "BY", "CALL", "CONNECT", "CYCLE", "MIN", "ABSOLUTE", "DICTIONARY", "MODIFY", "POSITION_REGEX", "SECOND", "UNDER", "CUME_DIST", "EXTRACT", "LEFT", "NORMALIZE", "REPLICATION", "BOTH", "BROWSE", "EXEC", "FORTRAN", "LANGUAGE", "RESTORE", "NOCHECK", "XMLTEXT", "FOR", "NEXT", "SHUTDOWN", "STRUCTURE", "BINARY", "CURRENT_TIMESTAMP", "DATE", "RETURN", "SAVE", "OVER", "BEGIN", "FULLTEXTTABLE", "IGNORE", "INPUT", "ISOLATION", "OUTPUT", "DEFERRABLE", "NONE", "COMMIT", "REGR_AVGX", "XMLCAST", "VAR_SAMP", "AT", "GLOBAL", "IS", "LN", "OR", "SEQUENCE", "TABLESAMPLE", "DOMAIN", "FREE", "IN", "LOCAL", "MEMBER", "OPENROWSET", "VARYING", "WRITE", "ARRAY", "COLUMN", "DYNAMIC", "OUTER", "DAY", "DEALLOCATE", "XMLPARSE", "CHARACTER_LENGTH", "PERCENT", "TRAN", "WRITETEXT", "PROCEDURE", "RECURSIVE", "struct{}{}", "CONTAINS", "OUT", "PLAN", "CURRENT_SCHEMA", "FALSE", "PRIOR", "RELEASE", "TRUNCATE", "XMLBINARY", "ANY", "DOUBLE", "IDENTITY_INSERT", "UPDATE", "CHAR", "LOCATOR", "ORDINALITY", "TSEQUAL", "XMLDOCUMENT", "CURRENT_ROLE", "KILL", "SMALLINT", "EACH", "MODULE", "RECONFIGURE", "SUBMULTISET", "THEN", "PASCAL", "CONNECTION", "LATERAL", "STATEMENT", "VALUE", "BIT_LENGTH", "XMLTABLE", "POSITION", "SIMILAR", "SOME", "UNIQUE", "BEFORE", "LOCALTIMESTAMP", "OPENQUERY", "ORDER", "ROLE", "SESSION_USER", "LEVEL", "PREORDER", "FUNCTION", "GROUP", "IDENTITY", "XMLAGG", "XMLQUERY", "CASCADED", "CAST", "CHECK", "RETURNS", "CASE", "CONTAINSTABLE", "LOCALTIME", "CALLED", "EQUALS", "READTEXT", "REGR_SLOPE", "WITHINGROUP", "AGGREGATE", "COLLATE", "INTERSECTION", "OVERLAPS", "STDDEV_POP", "UNKNOWN", "LOWER", "READS", "MATCH", "MERGE", "TRY_CONVERT", "CORR", "END", "HAVING", "SYMMETRIC", "UNION", "ADD", "DISCONNECT", "RULE", "STATISTICS", "WAITFOR", "FIRST", "INTERSECT", "PERCENT_RANK", "AFTER", "CATALOG", "DESTROY", "INSENSITIVE", "SIZE", "ARE", "OFFSETS", "REGR_COUNT", "ALTER", "AS", "COMPLETION", "COVAR_POP", "FILE", "SQLSTATE", "LESS", "MONTH", "ROUTINE", "CHARACTER", "CROSS", "REGR_SXX", "VARIABLE", "CONVERT", "FREETEXT", "REAL", "TIMEZONE_MINUTE", "TRANSLATE", "REVOKE", "VALUES", "BREAK", "COLLECT", "CURRENT_TRANSFORM_GROUP_FOR_TYPE", "DATA", "INOUT", "STATE", "TRANSLATION", "MAX", "SUM", "XMLCOMMENT", "COLLATION", "INITIALIZE", "LIMIT", "REGR_R2", "SETS", "SPECIFIC", "ASYMMETRIC", "COALESCE", "SEMANTICKEYPHRASETABLE", "SUBSTRING", "TERMINATE", "OPENXML", "PARAMETERS", "RESULT", "CURRENT_CATALOG", "GROUPING", "DESCRIBE", "DESTRUCTOR", "DISTRIBUTED", "BACKUP", "CLASS", "DELETE", "FOREIGN", "DBCC", "END-EXEC", "EXCEPT", "IF", "PRIVILEGES", "TIME", "WIDTH_BUCKET", "ALIAS", "CARDINALITY", "DEFERRED", "EXTERNAL", "IDENTITYCOL", "INDEX", "NAMES", "XMLSERIALIZE", "AVG", "CURRENT_USER", "HOST", "NULLIF", "OPTION", "REVERT", "USER", "NUMERIC", "PRINT", "REGR_INTERCEPT", "ALL", "CASCADE", "ELSE", "PRESERVE", "SENSITIVE", "HOLD", "REFERENCES", "SQLCA", "ACTION", "DECLARE", "LIKE", "PIVOT", "TRANSLATE_REGEX", "UNNEST", "AUTHORIZATION", "COMPUTE", "DEPTH", "JOIN", "OVERLAY", "XMLNAMESPACES", "LEADING", "PERCENTILE_DISC", "USE", "SQL", "VARCHAR", "ADMIN", "EXECUTE", "REFERENCING", "SQLCODE", "DEREF", "ERRLVL", "INDICATOR", "LOAD", "UPPER", "CLOSE", "CONSTRAINTS", "DECIMAL", "INITIALLY", "NATIONAL", "NCHAR", "SCHEMA", "CHAR_LENGTH", "DATABASE", "DUMP", "LIKE_REGEX", "TO", "PROC", "BLOB", "FUSION", "INNER", "RIGHT", "BOOLEAN", "CURRENT_DATE", "FILLFACTOR", "ROWCOUNT", "XMLVALIDATE", "CHECKPOINT", "EXCEPTION", "NATURAL", "ROLLBACK", "STDDEV_SAMP", "WINDOW", "CUBE", "CURRENT_PATH", "PREFIX", "TEXTSIZE", "WHERE", "DISK", "EVERY", "INSERT", "OCTET_LENGTH", "WHILE", "ASENSITIVE", "DROP", "HOLDLOCK", "BULK", "ONLY", "CONTINUE", "COUNT", "KEY", "BIT", "OF", "PRECISION", "TEMPORARY", "MULTISET", "PARAMETER", "RESTRICT", "SPACE", "TRAILING", "CLOB", "DISTINCT", "ESCAPE", "FETCH", "GRANT", "TRANSACTION", "ASSERTION", "FROM", "MODIFIES", "SQLWARNING", "XMLELEMENT", "THAN", "ASC", "EXISTS", "NEW", "OFF", "PERCENTILE_CONT", "PRIMARY", "ROW", "TREAT", "CURRENT_TIME", "ITERATE", "SET", "TOP", "XMLPI", "OPEN", "OPENDATASOURCE", "PARTIAL", "PREPARE", "REGR_AVGY", "SQLERROR", "STATIC", "ADA", "RANGE", "SCOPE", "GO", "NONCLUSTERED", "UPDATETEXT", "VIEW", "INT", "AND", "COVAR_SAMP", "DESC", "NOT", "ON", "TABLE", "INCLUDE", "SEARCH", "SPECIFICTYPE", "SQLEXCEPTION", "TRIGGER", "GET", "START", "DEFAULT", "FLOAT", "FOUND", "FULL", "USAGE", "GOTO", "RAISERROR", "SUBSTRING_REGEX", "ZONE", "BREADTH", "CURSOR", "OPERATION", "TIMEZONE_HOUR", "UESCAPE", "XMLCONCAT", "CURRENT_DEFAULT_TRANSFORM_GROUP", "ROLLUP", "INTEGER", "ROWS", "SAVEPOINT", "XMLITERATE", "YEAR", "ALLOCATE", "REGR_SXY", "POSTFIX", "SECTION", "WORK", "INTERVAL", "SCROLL", "WHEN", "FILTER", "XMLFOREST"], "AZURE_SYNAPSE": ["CAST", "GROUPING", "PIVOT", "ARE", "ATOMIC", "ORDINALITY", "OVERLAY", "SIZE", "AFTER", "EVERY", "GROUP", "REGR_SXX", "RESTRICT", "SEARCH", "STDDEV_POP", "COLLECT", "KEY", "LOCAL", "FROM", "TEMPORARY", "struct{}{}", "TSEQUAL", "WAITFOR", "BEFORE", "DEFERRED", "STATE", "XMLDOCUMENT", "ALLOCATE", "INOUT", "SYSTEM_USER", "CALL", "CALLED", "LIMIT", "INNER", "NONE", "REGR_SXY", "WHILE", "XMLQUERY", "AGGREGATE", "DICTIONARY", "INITIALIZE", "REVERT", "SIMILAR", "FOUND", "MODIFIES", "REFERENCES", "CONVERT", "PRESERVE", "DISTRIBUTED", "RULE", "READ", "SEQUENCE", "USE", "BREAK", "CURRENT_ROLE", "INTERVAL", "POSITION", "REFERENCING", "TRY_CONVERT", "WHERE", "CURRENT_DATE", "CURRENT_USER", "INSERT", "MIN", "PLAN", "SUBMULTISET", "CURRENT_TRANSFORM_GROUP_FOR_TYPE", "DATABASE", "FILE", "MERGE", "OPTION", "OFFSETS", "UPPER", "AND", "CHARACTER", "IDENTITY", "PRIMARY", "SELECT", "CASCADED", "DEALLOCATE", "SECOND", "SQL", "TABLE", "TIMEZONE_MINUTE", "NATIONAL", "NATURAL", "ROLE", "ERRLVL", "HAVING", "ONLY", "OFF", "SEMANTICSIMILARITYTABLE", "STDDEV_SAMP", "TRANSLATION", "CLASS", "CURRENT_TIMESTAMP", "ESCAPE", "WINDOW", "WITHOUT", "ASSERTION", "COVAR_POP", "OLD", "BETWEEN", "FREETEXT", "NORMALIZE", "PREORDER", "CONTAINSTABLE", "MAX", "PARAMETERS", "ANY", "BIT", "INTO", "FILLFACTOR", "IF", "NCLOB", "GENERAL", "MAP", "BOOLEAN", "COMMIT", "CROSS", "SUBSTRING_REGEX", "THAN", "CONNECT", "EXEC", "RIGHT", "SAVEPOINT", "DEC", "EXTERNAL", "PARTIAL", "DESCRIPTOR", "ORDER", "REGR_R2", "PREFIX", "SESSION", "COLLATE", "DEFAULT", "XMLITERATE", "CHARACTER_LENGTH", "LANGUAGE", "REPLICATION", "SET", "SPACE", "LEADING", "LOCATOR", "SECTION", "CURRENT_DEFAULT_TRANSFORM_GROUP", "PAD", "OVERLAPS", "SCROLL", "INPUT", "ROUTINE", "DESCRIBE", "FREE", "IN", "IDENTITY_INSERT", "OUTER", "SCOPE", "AT", "COLLATION", "FUNCTION", "RESTORE", "SEMANTICSIMILARITYDETAILSTABLE", "VARCHAR", "COALESCE", "MODULE", "REGR_AVGY", "DOUBLE", "SEMANTICKEYPHRASETABLE", "ISOLATION", "OVER", "INITIALLY", "LIKE_REGEX", "SETS", "VALUE", "CARDINALITY", "CURSOR", "INCLUDE", "PARTITION", "CURRENT_SCHEMA", "EXISTS", "INTEGER", "PASCAL", "SQLSTATE", "SQLWARNING", "BACKUP", "CHAR", "NULLIF", "HOLDLOCK", "LEVEL", "SPECIFIC", "TRANSLATE", "CURRENT_TIME", "END", "PROC", "XMLEXISTS", "OR", "READS", "TRANSLATE_REGEX", "EXCEPTION", "FILTER", "OPENQUERY", "PRECISION", "TRUNCATE", "ADA", "CLOSE", "EACH", "INT", "OPENXML", "DUMP", "FUSION", "GET", "XMLPARSE", "DBCC", "FIRST", "REF", "BIT_LENGTH", "PERCENT", "REGR_COUNT", "XMLTABLE", "PRINT", "ROWS", "WIDTH_BUCKET", "XMLCAST", "RELEASE", "ROW", "SCHEMA", "EQUALS", "OUT", "DEFERRABLE", "DISTINCT", "DROP", "PREPARE", "SAVE", "ASC", "LOCALTIME", "NEW", "LARGE", "SYSTEM", "WRITE", "PUBLIC", "RECURSIVE", "HOLD", "MOD", "NAMES", "SHUTDOWN", "CHECKPOINT", "CLOB", "FOREIGN", "CURRENT", "RANGE", "ABSOLUTE", "MODIFY", "XMLSERIALIZE", "TRANSACTION", "UNION", "DELETE", "EXIT", "IS", "LAST", "ADMIN", "DECIMAL", "KILL", "XMLCOMMENT", "COVAR_SAMP", "IDENTITYCOL", "SQLCA", "OPENROWSET", "REGR_SLOPE", "XMLELEMENT", "BREADTH", "DEPTH", "INDEX", "MATCH", "PERCENT_RANK", "XMLATTRIBUTES", "CUBE", "EXTRACT", "PRIOR", "UNPIVOT", "LEFT", "LOCALTIMESTAMP", "OPERATION", "LN", "POSITION_REGEX", "SMALLINT", "DATA", "SPECIFICTYPE", "XMLAGG", "TOP", "TRIGGER", "ASENSITIVE", "NONCLUSTERED", "SENSITIVE", "BY", "INTERSECT", "LINENO", "ROLLBACK", "CONTAINS", "EXECUTE", "LIKE", "ROWGUIDCOL", "SECURITYAUDIT", "XMLBINARY", "ARRAY", "BLOB", "ELEMENT", "ALTER", "MONTH", "XMLVALIDATE", "ASYMMETRIC", "DESC", "SETUSER", "OCCURRENCES_REGEX", "RETURN", "TO", "DIAGNOSTICS", "DISK", "METHOD", "BROWSE", "MULTISET", "FOR", "VARIABLE", "POSTFIX", "PROCEDURE", "ROWCOUNT", "VARYING", "DECLARE", "HOUR", "INTERSECTION", "INDICATOR", "LOWER", "ZONE", "NEXT", "SUBSTRING", "XMLNAMESPACES", "COMPLETION", "JOIN", "BEGIN", "CUME_DIST", "DYNAMIC", "VAR_POP", "WITHINGROUP", "WORK", "XMLFOREST", "ACTION", "OBJECT", "PERCENTILE_CONT", "ALL", "CHAR_LENGTH", "TIMEZONE_HOUR", "COLUMN", "FULLTEXTTABLE", "NULL", "TREAT", "UNIQUE", "FORTRAN", "OCTET_LENGTH", "RAISERROR", "CASE", "SQLCODE", "WRITETEXT", "OUTPUT", "AS", "CORR", "LATERAL", "REGR_SYY", "SUM", "TRAILING", "CURRENT_CATALOG", "DESTROY", "ELSE", "UNNEST", "BINARY", "CORRESPONDING", "DETERMINISTIC", "CASCADE", "INSENSITIVE", "VALUES", "END-EXEC", "GLOBAL", "UNDER", "RECONFIGURE", "BOTH", "ITERATE", "NO", "DESTRUCTOR", "REVOKE", "WITH", "CYCLE", "EXCEPT", "USAGE", "CHECK", "LESS", "ADD", "AVG", "REGR_INTERCEPT", "TABLESAMPLE", "IGNORE", "NOT", "SOME", "FULL", "GOTO", "OF", "STATISTICS", "TIME", "CLUSTERED", "DATE", "DAY", "VAR_SAMP", "DISCONNECT", "OPENDATASOURCE", "START", "SQLERROR", "UNKNOWN", "XMLTEXT", "COMPUTE", "HOST", "ON", "SYMMETRIC", "DOMAIN", "GRANT", "NCHAR", "FREETEXTTABLE", "CURRENT_PATH", "RETURNS", "UPDATETEXT", "YEAR", "CONSTRAINT", "READTEXT", "THEN", "CONTINUE", "MEMBER", "SESSION_USER", "ROLLUP", "TERMINATE", "UPDATE", "XMLCONCAT", "DEREF", "IMMEDIATE", "REGR_AVGX", "CONSTRAINTS", "PRIVILEGES", "FALSE", "LOAD", "RELATIVE", "CONDITION", "OPEN", "TRAN", "ALIAS", "CONNECTION", "WHENEVER", "SQLEXCEPTION", "COUNT", "NOCHECK", "PERCENTILE_DISC", "UESCAPE", "DENY", "FETCH", "TRIM", "CATALOG", "GO", "STATIC", "STATEMENT", "USER", "VIEW", "WITHIN", "AUTHORIZATION", "FLOAT", "RESULT", "TEXTSIZE", "USING", "WHEN", "MINUTE", "REAL", "STRUCTURE", "PARAMETER", "XMLPI", "BULK", "CREATE", "NUMERIC"], "BQ": ["ALL", "FROM", "NATURAL", "THEN", "UNBOUNDED", "USING", "ARRAY", "CURRENT", "IS", "NULLS", "TO", "TREAT", "RANGE", "BY", "CONTAINS", "END", "GROUP", "GROUPING", "HAVING", "CAST", "FETCH", "FULL", "INTERSECT", "PARTITION", "ENUM", "FOR", "LATERAL", "RECURSIVE", "CASE", "NULL", "UNION", "INNER", "NO", "CREATE", "CROSS", "CUBE", "EXCLUDE", "GROUPS", "IN", "WINDOW", "ANY", "DESC", "ESCAPE", "JOIN", "SOME", "IF", "LOOKUP", "DEFINE", "DISTINCT", "EXCEPT", "EXISTS", "FOLLOWING", "HASH", "ROLLUP", "ROWS", "WHERE", "ELSE", "BETWEEN", "NEW", "OUTER", "OVER", "RIGHT", "WITH", "FALSE", "IGNORE", "LEFT", "RESPECT", "SET", "EXTRACT", "OF", "OR", "PRECEDING", "TABLESAMPLE", "UNNEST", "AND", "ASSERT_ROWS_MODIFIED", "NOT", "struct{}{}", "ORDER", "PROTO", "AT", "COLLATE", "INTERVAL", "INTO", "LIKE", "ON", "STRUCT", "WITHIN", "WHEN", "AS", "ASC", "DEFAULT", "LIMIT", "MERGE", "SELECT"], "CLICKHOUSE": [], "DELTALAKE": ["INTERVAL", "NO", "TO", "USER", "CREATE", "OUTER", "FALSE", "FETCH", "WHEN", "WITH", "START", "CUBE", "CURRENT", "GROUP", "LATERAL", "PRIMARY", "ROW", "DESCRIBE", "DISTINCT", "EVENT_DATE", "NATURAL", "OR", "THEN", "AT", "CURRENT_DATE", "INTERSECT", "ROLLBACK", "UNION", "CASE", "FROM", "INNER", "ORDER", "ROWS", "OF", "REVOKE", "SET", "AS", "END", "BOTH", "CURRENT_TIME", "UPDATE", "USING", "GLOBAL", "VALUES", "EXCEPT", "OVERLAPS", "TRUNCATE", "BETWEEN", "FOREIGN", "JOIN", "ONLY", "ANY", "COLUMN", "ELSE", "FULL", "GROUPING", "POSITION", "SELECT", "ANTI", "UNIQUE", "EXTRACT", "FUNCTION", "UNKNOWN", "ALL", "AUTHORIZATION", "LOCAL", "OUT", "ROLLUP", "TABLESAMPLE", "EXISTS", "IS", "SESSION_USER", "struct{}{}", "ESCAPE", "EXTERNAL", "MINUS", "NOT", "REFERENCES", "CONSTRAINT", "FILTER", "INTO", "ALTER", "DELETE", "LEADING", "NULL", "RANGE", "TIME", "AND", "DROP", "HAVING", "IN", "CURRENT_USER", "CROSS", "ON", "WHERE", "COMMIT", "LEFT", "PARTITION", "TRAILING", "SOME", "TABLE", "BY", "CAST", "GRANT", "INSERT", "LIKE", "RIGHT", "ARRAY", "CHECK", "COLLATE", "FOR", "SEMI", "WINDOW", "CURRENT_TIMESTAMP"], "GCS_DATALAKE": ["WHEN", "DEFINE", "DISTINCT", "FROM", "STRUCT", "UNION", "struct{}{}", "USING", "DEFAULT", "HASH", "NEW", "ORDER", "PRECEDING", "ROWS", "CURRENT", "EXISTS", "JOIN", "LATERAL", "RECURSIVE", "INTO", "IS", "OR", "PROTO", "NATURAL", "OUTER", "UNBOUNDED", "ASSERT_ROWS_MODIFIED", "CASE", "COLLATE", "ENUM", "EXTRACT", "WHERE", "AND", "CAST", "MERGE", "GROUPS", "INNER", "NOT", "OF", "ROLLUP", "FOLLOWING", "FOR", "LOOKUP", "ARRAY", "BY", "CREATE", "CROSS", "END", "NULL", "ASC", "IGNORE", "UNNEST", "WITH", "AT", "GROUP", "INTERSECT", "RIGHT", "THEN", "PARTITION", "TABLESAMPLE", "WINDOW", "CUBE", "FETCH", "INTERVAL", "LIKE", "OVER", "SOME", "TO", "TREAT", "CONTAINS", "DESC", "LEFT", "RESPECT", "SELECT", "WITHIN", "ALL", "IF", "LIMIT", "AS", "ELSE", "RANGE", "ON", "SET", "ESCAPE", "EXCLUDE", "FALSE", "HAVING", "IN", "NO", "NULLS", "ANY", "BETWEEN", "EXCEPT", "FULL", "GROUPING"], "MSSQL": ["END-EXEC", "LOCATOR", "XMLSERIALIZE", "CONTAINSTABLE", "CURRENT_ROLE", "LEADING", "UESCAPE", "LAST", "CONNECT", "REGR_AVGX", "TRANSLATE_REGEX", "WITHIN", "XMLEXISTS", "ASC", "BIT_LENGTH", "XMLCAST", "XMLCOMMENT", "XMLFOREST", "DESCRIPTOR", "FREETEXT", "LARGE", "CONSTRAINT", "FOREIGN", "INITIALLY", "LINENO", "SMALLINT", "VALUE", "DATA", "SECTION", "TRANSLATE", "UNKNOWN", "BIT", "CURRENT_TRANSFORM_GROUP_FOR_TYPE", "GENERAL", "LOCALTIME", "NATIONAL", "UNDER", "ADMIN", "GRANT", "LIMIT", "LOCAL", "MERGE", "NEW", "WORK", "NCHAR", "NULLIF", "ROWGUIDCOL", "SEMANTICSIMILARITYDETAILSTABLE", "SUBSTRING", "WRITETEXT", "FILLFACTOR", "IF", "NOCHECK", "PERCENTILE_DISC", "RECURSIVE", "TREAT", "READTEXT", "WRITE", "NCLOB", "RETURNS", "XMLBINARY", "DECLARE", "FETCH", "NULL", "REVOKE", "STATE", "SYMMETRIC", "CLUSTERED", "COVAR_POP", "CURSOR", "IN", "NOT", "SIZE", "POSITION_REGEX", "ARRAY", "DISTRIBUTED", "DROP", "OPENROWSET", "PREPARE", "SECOND", "UNNEST", "DISCONNECT", "THEN", "EXCEPTION", "FREE", "OVERLAY", "SCROLL", "ROW", "CURRENT", "IGNORE", "LESS", "MODIFIES", "OPENQUERY", "RANGE", "DEC", "OFFSETS", "SQL", "AFTER", "CLASS", "CHAR_LENGTH", "MONTH", "REFERENCES", "REGR_COUNT", "ROLLBACK", "ANY", "CURRENT_SCHEMA", "RESTRICT", "TIME", "XMLQUERY", "XMLVALIDATE", "WHERE", "ASSERTION", "ELSE", "NO", "PARAMETERS", "PREORDER", "USAGE", "CURRENT_TIMESTAMP", "DISK", "SIMILAR", "SYSTEM_USER", "CHAR", "CONNECTION", "DELETE", "KILL", "XMLELEMENT", "XMLTABLE", "DISTINCT", "EXCEPT", "HOLDLOCK", "OLD", "PRESERVE", "STATEMENT", "CAST", "COLLATION", "COUNT", "OCCURRENCES_REGEX", "START", "SUBMULTISET", "SUM", "CONDITION", "DESC", "LOWER", "REGR_R2", "SOME", "SQLCODE", "WINDOW", "ACTION", "INTERSECT", "REGR_SXX", "DAY", "DESCRIBE", "TERMINATE", "WITHOUT", "DEALLOCATE", "DENY", "REGR_SYY", "USER", "XMLPI", "PUBLIC", "TRY_CONVERT", "ALTER", "AS", "OCTET_LENGTH", "RESTORE", "RULE", "SPECIFICTYPE", "CASE", "EXTERNAL", "KEY", "XMLCONCAT", "EQUALS", "INPUT", "LEVEL", "ONLY", "READ", "SQLERROR", "SEARCH", "SENSITIVE", "DEFERRED", "INDICATOR", "PAD", "STATISTICS", "XMLITERATE", "EACH", "LOCALTIMESTAMP", "PREFIX", "REFERENCING", "ADA", "COLLECT", "DBCC", "MAP", "METHOD", "OPENDATASOURCE", "REF", "ABSOLUTE", "CUBE", "DIAGNOSTICS", "MULTISET", "RETURN", "WHEN", "GOTO", "BEFORE", "CHARACTER_LENGTH", "OR", "PLAN", "SPECIFIC", "TO", "LOAD", "MAX", "OPTION", "SCOPE", "STDDEV_SAMP", "XMLDOCUMENT", "CARDINALITY", "TIMEZONE_MINUTE", "TRIGGER", "VIEW", "IDENTITY", "STDDEV_POP", "ADD", "ALLOCATE", "DOMAIN", "RESULT", "ROWCOUNT", "TSEQUAL", "TABLE", "XMLTEXT", "ALIAS", "ASENSITIVE", "CURRENT_TIME", "FILE", "PRINT", "ROLLUP", "THAN", "TRIM", "IDENTITY_INSERT", "ON", "SESSION", "UNIQUE", "YEAR", "VARCHAR", "CHECKPOINT", "DETERMINISTIC", "EVERY", "MATCH", "NUMERIC", "PARAMETER", "DYNAMIC", "HAVING", "INITIALIZE", "REGR_AVGY", "CURRENT_DATE", "EXISTS", "PERCENTILE_CONT", "ARE", "END", "GROUPING", "SETUSER", "SQLWARNING", "BREAK", "DECIMAL", "LANGUAGE", "PARTITION", "SEQUENCE", "SETS", "TRANSACTION", "struct{}{}", "XMLAGG", "BOOLEAN", "BY", "CLOSE", "CONTAINS", "INNER", "VALUES", "OPENXML", "PRIOR", "TABLESAMPLE", "TRANSLATION", "BETWEEN", "CUME_DIST", "ELEMENT", "EXEC", "HOST", "LATERAL", "USING", "CONTINUE", "INDEX", "NONE", "SELECT", "REGR_SLOPE", "WHILE", "AND", "DICTIONARY", "MIN", "PERCENT_RANK", "UPDATE", "UPDATETEXT", "TRUNCATE", "CASCADE", "CONVERT", "DEFERRABLE", "FILTER", "FLOAT", "NAMES", "VAR_POP", "DESTRUCTOR", "ISOLATION", "OFF", "OUTPUT", "VAR_SAMP", "CONSTRAINTS", "SQLSTATE", "CHARACTER", "OUTER", "TIMEZONE_HOUR", "CALLED", "CYCLE", "INSENSITIVE", "TEXTSIZE", "ATOMIC", "COMPLETION", "PROCEDURE", "REGR_INTERCEPT", "BREADTH", "FULLTEXTTABLE", "DATABASE", "DEREF", "WITH", "SPACE", "VARIABLE", "COALESCE", "CURRENT_DEFAULT_TRANSFORM_GROUP", "ESCAPE", "FORTRAN", "OVERLAPS", "BINARY", "CATALOG", "CREATE", "UNION", "WAITFOR", "LN", "SCHEMA", "SYSTEM", "INTERSECTION", "ROWS", "CALL", "UPPER", "UNPIVOT", "BLOB", "FALSE", "GET", "INSERT", "LEFT", "PIVOT", "TEMPORARY", "USE", "PRIMARY", "SEMANTICKEYPHRASETABLE", "MEMBER", "COVAR_SAMP", "CURRENT_PATH", "SECURITYAUDIT", "BOTH", "OF", "WIDTH_BUCKET", "BROWSE", "CORR", "PRIVILEGES", "READS", "CURRENT_CATALOG", "SHUTDOWN", "FOR", "FUSION", "IDENTITYCOL", "MODIFY", "ALL", "XMLPARSE", "COMMIT", "RIGHT", "XMLATTRIBUTES", "XMLNAMESPACES", "TRAILING", "TRAN", "BULK", "INTEGER", "RAISERROR", "FOUND", "INCLUDE", "VARYING", "DATE", "FROM", "JOIN", "STRUCTURE", "AVG", "DEFAULT", "GO", "NATURAL", "ORDINALITY", "SAVEPOINT", "EXIT", "GROUP", "WHENEVER", "AGGREGATE", "DOUBLE", "EXECUTE", "POSTFIX", "CURRENT_USER", "DESTROY", "FIRST", "ROUTINE", "TOP", "REAL", "MODULE", "ASYMMETRIC", "CROSS", "FREETEXTTABLE", "PERCENT", "INT", "OVER", "PROC", "SUBSTRING_REGEX", "WITHINGROUP", "FULL", "FUNCTION", "NORMALIZE", "RELEASE", "REPLICATION", "SAVE", "HOUR", "LIKE_REGEX", "NONCLUSTERED", "SQLEXCEPTION", "ZONE", "AT", "COLUMN", "EXTRACT", "MINUTE", "MOD", "CASCADED", "COMPUTE", "GLOBAL", "OPEN", "ORDER", "PARTIAL", "PRECISION", "SQLCA", "AUTHORIZATION", "DEPTH", "INOUT", "INTO", "OBJECT", "OUT", "STATIC", "ITERATE", "REGR_SXY", "SET", "CLOB", "LIKE", "DUMP", "ERRLVL", "POSITION", "RECONFIGURE", "BEGIN", "CHECK", "INTERVAL", "NEXT", "RELATIVE", "BACKUP", "CORRESPONDING", "HOLD", "IMMEDIATE", "SEMANTICSIMILARITYTABLE", "PASCAL", "SESSION_USER", "COLLATE", "IS", "OPERATION", "REVERT", "ROLE"], "POSTGRES": ["USING", "BINARY", "DESC", "EXCEPT", "FOREIGN", "NULL", "REFERENCES", "SYMMETRIC", "struct{}{}", "ANY", "CROSS", "DEFAULT", "DISTINCT", "FALSE", "OLD", "SESSION_USER", "TABLE", "CHECK", "CURRENT_TIME", "FOR", "FREEZE", "LIMIT", "LOCALTIME", "NOT", "ARRAY", "COLLATE", "OFF", "OFFSET", "BETWEEN", "CONSTRAINT", "CURRENT_TIMESTAMP", "SIMILAR", "UNION", "LEADING", "LIKE", "ON", "UNIQUE", "AUTHORIZATION", "CURRENT_DATE", "ILIKE", "INNER", "VERBOSE", "CURRENT_USER", "IN", "NOTNULL", "ORDER", "THEN", "TRAILING", "WHEN", "LOCALTIMESTAMP", "NEW", "OUTER", "JOIN", "ASC", "DO", "FROM", "HAVING", "INTERSECT", "INTO", "ELSE", "ALL", "ANALYSE", "AS", "ASYMMETRIC", "CAST", "CREATE", "DEFERRABLE", "FULL", "ISNULL", "OR", "PRIMARY", "RIGHT", "END", "GRANT", "OVERLAPS", "PLACING", "USER", "WHERE", "ANALYZE", "BOTH", "GROUP", "INITIALLY", "IS", "NATURAL", "SOME", "AND", "CASE", "COLUMN", "CURRENT_ROLE", "LEFT", "ONLY", "SELECT"], "RS": ["TAG", "AES128", "OFFSET", "OLD", "PLACING", "THEN", "struct{}{}", "WALLET", "CREATE", "DEFAULT", "DEFRAG", "LUNS", "TEXT255", "LOCALTIME", "LZO", "RESPECT", "WHERE", "ASC", "DEFERRABLE", "DELTA32K", "AND", "CURRENT_TIMESTAMP", "OPEN", "WITH", "ALL", "ENABLE", "EXCEPT", "NATURAL", "REJECTLOG", "CONSTRAINT", "CREDENTIALS", "MOSTLY8", "PERCENT", "REFERENCES", "TO", "USING", "BLANKSASNULL", "INTO", "LANGUAGE", "ANY", "READRATIO", "BACKUP", "GZIP", "UNION", "CURRENT_USER", "BETWEEN", "BOTH", "BZIP2", "JOIN", "TOP", "TABLE", "USER", "COLLATE", "NOT", "ONLY", "NEW", "ORDER", "SIMILAR", "COLUMN", "CROSS", "IGNORE", "WHEN", "GLOBALDICT64K", "GRANT", "RIGHT", "ON", "SYSTEM", "ELSE", "ISNULL", "MINUS", "ILIKE", "MOSTLY13", "RESTORE", "DO", "FOR", "FREEZE", "INTERSECT", "IS", "UNIQUE", "END", "LEADING", "OFF", "RECOVER", "AUTHORIZATION", "BYTEDICT", "CURRENT_TIME", "ARRAY", "DELTA", "IN", "PARALLEL", "RAW", "SELECT", "TDES", "CAST", "EXPLICIT", "OR", "NULL", "OVERLAPS", "CHECK", "EMPTYASNULL", "GLOBALDICT256", "INNER", "RESORT", "CASE", "ENCODE", "FALSE", "SESSION_USER", "TEXT32K", "ALLOWOVERWRITE", "AZ64", "PERMISSIONS", "LEFT", "DESC", "HAVING", "INITIALLY", "AES256", "ENCRYPT     ", "LUN", "SYSDATE", "FROM", "GROUP", "PRIMARY", "DEFLATE", "FOREIGN", "LOCALTIMESTAMP", "NOTNULL", "OUTER", "AS", "BINARY", "CURRENT_DATE", "TRUNCATECOLUMNS", "VERBOSE", "NULLS", "SOME", "ANALYSE", "ANALYZE", "FULL", "OFFLINE", "SNAPSHOT ", "CURRENT_USER_ID", "DISABLE", "LIKE", "LZOP", "TRAILING", "WITHOUT", "ENCRYPTION", "IDENTITY", "LIMIT", "PARTITION", "TIMESTAMP", "DISTINCT", "MOSTLY32", "OID"], "S3_DATALAKE": ["INTERSECT", "LOCAL", "SET", "TIMESTAMP", "struct{}{}", "CONSTRAINT", "CREATE", "NUMERIC", "EXCHANGE", "LEFT", "COMMIT", "CURSOR", "DESCRIBE", "LATERAL", "RANGE", "ORDER", "PERCENT", "BOTH", "EXTRACT", "FETCH", "FOLLOWING", "GROUP", "NONE", "TRIGGER", "TRUNCATE", "WHEN", "BY", "COLUMN", "EXISTS", "MACRO", "THEN", "CASE", "CHAR", "DECIMAL", "PRIMARY", "RLIKE", "REFERENCES", "RIGHT", "TABLE", "VIEWS", "EXTENDED", "OVER", "TO", "PARTITION", "UNBOUNDED", "USING", "INT", "NOT", "SMALLINT", "ROW", "ROWS", "CURRENT_TIMESTAMP", "DOUBLE", "FOR", "IMPORT", "LESS", "OR", "ARRAY", "DISTINCT", "ONLY", "CONF", "HAVING", "JOIN", "OF", "OUT", "SELECT", "CAST", "TABLESAMPLE", "GROUPING", "INNER", "END", "PRECEDING", "OUTER", "INTERVAL", "REDUCE", "ROLLBACK", "UNION", "WINDOW", "FUNCTION", "ON", "START", "BETWEEN", "DATABASE", "DAYOFWEEK", "FOREIGN", "GRANT", "MORE", "CURRENT_DATE", "READS", "USER", "WHERE", "ALL", "FALSE", "TRANSFORM", "VARCHAR", "WITH", "ELSE", "INSERT", "INTEGER", "INTO", "IS", "DATE", "NULL", "REGEXP", "REVOKE", "AUTHORIZATION", "BIGINT", "BOOLEAN", "FLOOR", "MAP", "CUBE", "FLOAT", "FULL", "ALTER", "CURRENT", "PARTIALSCAN", "ROLLUP", "UNIQUEJOIN", "UTC_TIMESTAMP", "CASHE", "DELETE", "EXTERNAL", "LIKE", "BINARY", "CROSS", "FROM", "PRECISION", "PRESERVE", "TIME", "UPDATE", "AND", "AS", "DROP", "IF", "IN", "PROCEDURE", "VALUES"], "SNOWFLAKE": ["CHECK", "DATABASE", "ORGANIZATION", "SELECT", "UPDATE", "WHERE", "AS", "CONSTRAINT", "CROSS", "ELSE", "IN", "LOCALTIMESTAMP", "OF", "ON", "ALTER", "ROWS", "WHEN", "RIGHT", "LEFT", "NATURAL", "GSCLUSTER", "COLUMN", "CURRENT", "ROW", "TRIGGER", "TRY_CAST", "WHENEVER", "ACCOUNT", "LOCALTIME", "ORDER", "GRANT", "HAVING", "INCREMENT", "MINUS", "OR", "RLIKE", "SAMPLE", "VALUES", "FULL", "VIEW", "DELETE", "DROP", "ISSUE", "ANY", "DISTINCT", "EXISTS", "INTO", "NOT", "NULL", "TO", "CURRENT_DATE", "FOR", "SCHEMA", "TABLESAMPLE", "struct{}{}", "ALL", "CURRENT_TIMESTAMP", "FROM", "LIKE", "QUALIFY", "CONNECTION", "CASE", "CURRENT_USER", "INNER", "LATERAL", "THEN", "UNION", "AND", "FALSE", "USING", "CREATE", "BY", "CONNECT", "CURRENT_TIME", "FOLLOWING", "ILIKE", "INSERT", "JOIN", "BETWEEN", "START", "TABLE", "SOME", "GROUP", "INTERSECT", "REGEXP", "CAST", "REVOKE", "SET", "UNIQUE", "WITH", "IS"], "SNOWPIPE_STREAMING": ["CHECK", "DATABASE", "ORGANIZATION", "SELECT", "UPDATE", "WHERE", "AS", "CONSTRAINT", "CROSS", "ELSE", "IN", "LOCALTIMESTAMP", "OF", "ON", "ALTER", "ROWS", "WHEN", "RIGHT", "LEFT", "NATURAL", "GSCLUSTER", "COLUMN", "CURRENT", "ROW", "TRIGGER", "TRY_CAST", "WHENEVER", "ACCOUNT", "LOCALTIME", "ORDER", "GRANT", "HAVING", "INCREMENT", "MINUS", "OR", "RLIKE", "SAMPLE", "VALUES", "FULL", "VIEW", "DELETE", "DROP", "ISSUE", "ANY", "DISTINCT", "EXISTS", "INTO", "NOT", "NULL", "TO", "CURRENT_DATE", "FOR", "SCHEMA", "TABLESAMPLE", "struct{}{}", "ALL", "CURRENT_TIMESTAMP", "FROM", "LIKE", "QUALIFY", "CONNECTION", "CASE", "CURRENT_USER", "INNER", "LATERAL", "THEN", "UNION", "AND", "FALSE", "USING", "CREATE", "BY", "CONNECT", "CURRENT_TIME", "FOLLOWING", "ILIKE", "INSERT", "JOIN", "BETWEEN", "START", "TABLE", "SOME", "GROUP", "INTERSECT", "REGEXP", "CAST", "REVOKE", "SET", "UNIQUE", "WITH", "IS"]}