{"AZURE_DATALAKE": ["NULL", "PREORDER", "BOTH", "OVERLAY", "REGR_SXX", "ROW", "START", "ASSERTION", "OLD", "ON", "PROC", "RULE", "ALTER", "COMMIT", "CONTINUE", "NCLOB", "EXCEPTION", "HOST", "READS", "USE", "VALUE", "AFTER", "EXTERNAL", "FULL", "PARAMETER", "MATCH", "SQLSTATE", "TREAT", "SQLCODE", "YEAR", "DEALLOCATE", "ROLLBACK", "STATISTICS", "TABLE", "DISTINCT", "INNER", "SENSITIVE", "SEQUENCE", "ABSOLUTE", "CURRENT_TIMESTAMP", "FOUND", "ROLE", "SMALLINT", "VARCHAR", "FROM", "MONTH", "OUTER", "TIMEZONE_MINUTE", "FILTER", "GROUPING", "NULLIF", "OUT", "PERCENTILE_CONT", "WITHINGROUP", "SELECT", "XMLCONCAT", "CONTAINS", "EXECUTE", "LATERAL", "RETURNS", "SAVE", "SECURITYAUDIT", "COVAR_POP", "KILL", "XMLITERATE", "BROWSE", "CORRESPONDING", "TRY_CONVERT", "CARDINALITY", "DUMP", "EXEC", "DISCONNECT", "NOCHECK", "CURRENT_TRANSFORM_GROUP_FOR_TYPE", "DOMAIN", "ERRLVL", "NEXT", "DATE", "LARGE", "NORMALIZE", "REGR_SYY", "REVERT", "TEMPORARY", "PUBLIC", "RELEASE", "DROP", "INITIALLY", "CALL", "CASCADE", "TRAILING", "CASCADED", "HOUR", "NATIONAL", "SAVEPOINT", "USER", "INITIALIZE", "NUMERIC", "OPENROWSET", "OPERATION", "REGR_AVGY", "ROWS", "CONNECT", "DEFERRED", "IDENTITYCOL", "VARYING", "XMLCOMMENT", "BULK", "COMPUTE", "FREE", "FUSION", "NONE", "OBJECT", "COLLECT", "COLUMN", "FORTRAN", "CATALOG", "DECIMAL", "FLOAT", "CONNECTION", "CURSOR", "MEMBER", "TEXTSIZE", "TRUE", "CUME_DIST", "PERCENTILE_DISC", "CONDITION", "DESC", "FOREIGN", "LEADING", "LOCATOR", "NO", "DAY", "MOD", "PRECISION", "REFERENCING", "VAR_POP", "ZONE", "CHAR", "INPUT", "INTERSECTION", "LOCALTIMESTAMP", "NEW", "SIZE", "BY", "INSERT", "PREFIX", "XMLCAST", "DIAGNOSTICS", "SYSTEM_USER", "BIT", "CROSS", "ONLY", "SYMMETRIC", "DEREF", "TRANSACTION", "END-EXEC", "MIN", "UPPER", "CURRENT_ROLE", "SETS", "TRANSLATE_REGEX", "ELEMENT", "LESS", "PRINT", "REGR_COUNT", "ALLOCATE", "CHAR_LENGTH", "CYCLE", "EXCEPT", "NATURAL", "OPTION", "STRUCTURE", "TRIGGER", "DELETE", "BINARY", "DISK", "SQLWARNING", "CONSTRAINTS", "FIRST", "NONCLUSTERED", "WHENEVER", "CLUSTERED", "INTEGER", "SIMILAR", "NAMES", "TIME", "TRIM", "CLASS", "EXIT", "INDICATOR", "MAP", "ROLLUP", "FREETEXTTABLE", "MODIFY", "ASC", "CASE", "GLOBAL", "OPEN", "REGR_SXY", "ACTION", "ASENSITIVE", "LOWER", "REGR_R2", "WRITE", "FALSE", "REGR_AVGX", "SCROLL", "WHEN", "POSTFIX", "TRUNCATE", "BREADTH", "CURRENT_DATE", "PRIOR", "SEMANTICSIMILARITYDETAILSTABLE", "IDENTITY_INSERT", "CORR", "FOR", "OF", "ALL", "CLOB", "MODIFIES", "TRANSLATION", "EACH", "ESCAPE", "PAD", "REFERENCES", "SPECIFIC", "SUM", "WRITETEXT", "XMLPI", "DISTRIBUTED", "GROUP", "IMMEDIATE", "INCLUDE", "POSITION", "SUBSTRING", "ASYMMETRIC", "BEGIN", "OCCURRENCES_REGEX", "RESTRICT", "SPECIFICTYPE", "DBCC", "DICTIONARY", "KEY", "NCHAR", "RESULT", "AS", "COLLATE", "CURRENT", "OR", "REGR_SLOPE", "EXTRACT", "ORDER", "SEMANTICSIMILARITYTABLE", "TSEQUAL", "XMLDOCUMENT", "LIMIT", "TERMINATE", "ADA", "CLOSE", "EQUALS", "OFF", "VARIABLE", "END", "PASCAL", "TO", "VAR_SAMP", "ADD", "COALESCE", "RESTORE", "USING", "VIEW", "ANY", "CHARACTER", "IDENTITY", "ISOLATION", "STATE", "FILE", "GOTO", "MULTISET", "WITH", "AVG", "STATIC", "XMLQUERY", "ARRAY", "CURRENT_PATH", "PARTIAL", "VALUES", "XMLAGG", "WHILE", "ARE", "BETWEEN", "CUBE", "CURRENT_TIME", "PREPARE", "READ", "EXISTS", "GENERAL", "JOIN", "MAX", "OPENXML", "UNDER", "SCHEMA", "AT", "FREETEXT", "LOCAL", "XMLVALIDATE", "DYNAMIC", "DEPTH", "OPENDATASOURCE", "POSITION_REGEX", "SQLEXCEPTION", "AND", "OVER", "OVERLAPS", "PLAN", "THEN", "XMLEXISTS", "CURRENT_SCHEMA", "HAVING", "RELATIVE", "RIGHT", "SQLCA", "SYSTEM", "CONVERT", "PRIMARY", "REF", "SCOPE", "UPDATE", "BOOLEAN", "DATABASE", "XMLFOREST", "EVERY", "LEVEL", "NOT", "ROUTINE", "GO", "LOAD", "SECTION", "USAGE", "CHECKPOINT", "DECLARE", "PRIVILEGES", "INOUT", "METHOD", "XMLNAMESPACES", "TOP", "COVAR_SAMP", "IF", "LINENO", "PRESERVE", "PROCEDURE", "STATEMENT", "ATOMIC", "BIT_LENGTH", "HOLD", "AUTHORIZATION", "COLLATION", "DETERMINISTIC", "READTEXT", "CONSTRAINT", "LANGUAGE", "THAN", "WITHIN", "CONTAINSTABLE", "XMLSERIALIZE", "FULLTEXTTABLE", "INDEX", "MINUTE", "DENY", "FUNCTION", "RECONFIGURE", "XMLATTRIBUTES", "INTO", "RETURN", "SQLERROR", "SUBMULTISET", "XMLBINARY", "CURRENT_CATALOG", "LOCALTIME", "STDDEV_SAMP", "AGGREGATE", "DESTRUCTOR", "OCTET_LENGTH", "PERCENT_RANK", "SESSION", "UPDATETEXT", "DESCRIBE", "RAISERROR", "RANGE", "ROWGUIDCOL", "TRAN", "CAST", "DEC", "DEFERRABLE", "FETCH", "OUTPUT", "WIDTH_BUCKET", "WITHOUT", "BEFORE", "ORDINALITY", "SESSION_USER", "WORK", "CURRENT_USER", "SQL", "SUBSTRING_REGEX", "XMLPARSE", "DATA", "INTERVAL", "MODULE", "SHUTDOWN", "TIMEZONE_HOUR", "UNIQUE", "ADMIN", "ALIAS", "CALLED", "CREATE", "LAST", "RECURSIVE", "UNPIVOT", "COUNT", "INSENSITIVE", "SECOND", "SOME", "STDDEV_POP", "UNION", "FILLFACTOR", "PERCENT", "REVOKE", "BLOB", "BREAK", "DEFAULT", "IN", "LEFT", "REPLICATION", "DESCRIPTOR", "ELSE", "OPENQUERY", "SET", "GRANT", "PARAMETERS", "UESCAPE", "WAITFOR", "UNNEST", "HOLDLOCK", "LN", "WINDOW", "DOUBLE", "LIKE", "REAL", "SEMANTICKEYPHRASETABLE", "XMLELEMENT", "IGNORE", "ROWCOUNT", "TRANSLATE", "UNKNOWN", "XMLTABLE", "CURRENT_DEFAULT_TRANSFORM_GROUP", "OFFSETS", "SETUSER", "COMPLETION", "XMLTEXT", "CHARACTER_LENGTH", "GET", "INT", "MERGE", "REGR_INTERCEPT", "DESTROY", "PARTITION", "TABLESAMPLE", "WHERE", "INTERSECT", "ITERATE", "PIVOT", "SEARCH", "SPACE", "BACKUP", "CHECK", "IS", "LIKE_REGEX"], "AZURE_SYNAPSE": ["BEGIN", "LEVEL", "LOCAL", "PRINT", "TRUNCATE", "LINENO", "NEW", "OLD", "RELEASE", "ROW", "CASCADE", "FOR", "INTERVAL", "WRITETEXT", "FILTER", "SAVEPOINT", "UNION", "INTEGER", "MIN", "XMLBINARY", "DEREF", "RELATIVE", "SQLWARNING", "TEXTSIZE", "CURRENT_TIMESTAMP", "PROC", "SPACE", "START", "REGR_R2", "COLLECT", "CROSS", "FULLTEXTTABLE", "OCTET_LENGTH", "PRECISION", "REPLICATION", "ROWCOUNT", "THEN", "CURRENT_ROLE", "INITIALIZE", "NCLOB", "PIVOT", "CREATE", "NULL", "SIZE", "SMALLINT", "SPECIFICTYPE", "SQLERROR", "XMLTABLE", "CORRESPONDING", "DICTIONARY", "MEMBER", "ON", "WITHINGROUP", "AT", "LEFT", "PREFIX", "VAR_SAMP", "XMLCOMMENT", "DENY", "EXTERNAL", "INTO", "PREPARE", "SQL", "DESCRIPTOR", "WHERE", "XMLFOREST", "XMLPI", "COMMIT", "FILE", "LOAD", "CONTAINSTABLE", "CUBE", "REF", "VARCHAR", "XMLELEMENT", "DATE", "LOWER", "SYMMETRIC", "VAR_POP", "XMLAGG", "INPUT", "DECIMAL", "DEPTH", "GLOBAL", "STRUCTURE", "FIRST", "STDDEV_SAMP", "ADMIN", "BOOLEAN", "ELEMENT", "HOUR", "AFTER", "BIT_LENGTH", "PARTIAL", "PERCENTILE_CONT", "REGR_AVGX", "ALLOCATE", "HOLDLOCK", "REGR_AVGY", "CUME_DIST", "INCLUDE", "CONNECTION", "OVERLAPS", "DYNAMIC", "RULE", "TRAILING", "COLUMN", "LIMIT", "PARTITION", "SETS", "VARYING", "BEFORE", "BY", "IDENTITYCOL", "NCHAR", "OBJECT", "ADD", "RETURNS", "SELECT", "EQUALS", "XMLDOCUMENT", "CALL", "EXECUTE", "ORDER", "VALUES", "ADA", "ROLLBACK", "DISK", "DISTRIBUTED", "PERCENTILE_DISC", "STATIC", "UNNEST", "XMLCAST", "CONVERT", "FOREIGN", "HAVING", "MERGE", "STATISTICS", "XMLEXISTS", "ALIAS", "MODIFY", "RESTRICT", "TRUE", "OPENXML", "BETWEEN", "CHARACTER", "CONNECT", "LARGE", "LATERAL", "KILL", "POSTFIX", "READS", "SECTION", "CURSOR", "IS", "TRY_CONVERT", "WHEN", "COLLATE", "LANGUAGE", "SEMANTICSIMILARITYTABLE", "UNIQUE", "CONDITION", "IN", "REVERT", "XMLCONCAT", "AND", "AS", "CALLED", "DOUBLE", "HOLD", "XMLSERIALIZE", "AUTHORIZATION", "CHAR", "ELSE", "END-EXEC", "LOCALTIMESTAMP", "BREAK", "DIAGNOSTICS", "SAVE", "SESSION", "DATA", "PERCENT_RANK", "USING", "ROLE", "ABSOLUTE", "ANY", "INSENSITIVE", "ISOLATION", "MATCH", "ACTION", "LOCATOR", "REGR_COUNT", "SEARCH", "BOTH", "NULLIF", "REFERENCING", "TABLESAMPLE", "FILLFACTOR", "OVER", "COMPUTE", "DROP", "LEADING", "NUMERIC", "REGR_SXY", "BIT", "CLUSTERED", "TOP", "WITH", "NATIONAL", "NONCLUSTERED", "SQLEXCEPTION", "COLLATION", "DOMAIN", "ESCAPE", "GROUPING", "LIKE", "TABLE", "CLASS", "INOUT", "PARAMETER", "SUM", "WIDTH_BUCKET", "WITHOUT", "XMLITERATE", "COMPLETION", "GO", "GRANT", "NORMALIZE", "REVOKE", "DISCONNECT", "NO", "REAL", "SQLSTATE", "THAN", "UNPIVOT", "CARDINALITY", "CYCLE", "OPENQUERY", "OUTER", "SETUSER", "UPDATETEXT", "AGGREGATE", "CONTINUE", "POSITION", "RANGE", "SET", "GROUP", "LAST", "PERCENT", "ASC", "EACH", "FALSE", "INSERT", "OPTION", "INDEX", "MINUTE", "OFFSETS", "OUTPUT", "WINDOW", "OCCURRENCES_REGEX", "TRIGGER", "USE", "EXISTS", "RESTORE", "FUSION", "PARAMETERS", "PLAN", "FORTRAN", "FUNCTION", "CASCADED", "COUNT", "PASCAL", "EXEC", "XMLVALIDATE", "CATALOG", "RETURN", "ROLLUP", "SIMILAR", "WORK", "DECLARE", "PAD", "PRIMARY", "REGR_SXX", "REGR_SYY", "SENSITIVE", "TIMEZONE_MINUTE", "TRANSACTION", "VIEW", "XMLPARSE", "DETERMINISTIC", "CHAR_LENGTH", "IGNORE", "JOIN", "REGR_SLOPE", "DEALLOCATE", "FETCH", "OR", "RECURSIVE", "CURRENT_CATALOG", "READTEXT", "UNDER", "WHILE", "INDICATOR", "INT", "MONTH", "XMLQUERY", "XMLTEXT", "CAST", "COVAR_SAMP", "RIGHT", "SECURITYAUDIT", "UPPER", "ASYMMETRIC", "CURRENT_PATH", "LOCALTIME", "NEXT", "REGR_INTERCEPT", "TRIM", "USER", "CONSTRAINTS", "DEFERRED", "DESTROY", "OPENROWSET", "SCHEMA", "CONSTRAINT", "DESC", "ONLY", "SYSTEM_USER", "TIME", "DEC", "DESTRUCTOR", "RESULT", "DAY", "IMMEDIATE", "OPERATION", "PROCEDURE", "SQLCA", "UNKNOWN", "WRITE", "GENERAL", "GET", "METHOD", "ORDINALITY", "PRESERVE", "CLOB", "OPENDATASOURCE", "WAITFOR", "OUT", "TEMPORARY", "TRANSLATE", "COVAR_POP", "BLOB", "OPEN", "POSITION_REGEX", "TRAN", "YEAR", "BULK", "CASE", "HOST", "SECOND", "CURRENT_DEFAULT_TRANSFORM_GROUP", "NAMES", "NOCHECK", "OFF", "ROWS", "WHENEVER", "BACKUP", "IDENTITY", "SEMANTICKEYPHRASETABLE", "SESSION_USER", "STATEMENT", "STDDEV_POP", "ALL", "CHECK", "CURRENT", "DBCC", "FROM", "SYSTEM", "ASSERTION", "DUMP", "LN", "CHARACTER_LENGTH", "EXCEPT", "INITIALLY", "NOT", "ROWGUIDCOL", "OF", "ZONE", "SUBSTRING_REGEX", "DEFAULT", "DEFERRABLE", "EVERY", "MODIFIES", "SEQUENCE", "ATOMIC", "ROUTINE", "ASENSITIVE", "BROWSE", "IDENTITY_INSERT", "TREAT", "ARRAY", "FOUND", "GOTO", "MULTISET", "SHUTDOWN", "TO", "CLOSE", "ERRLVL", "IF", "XMLNAMESPACES", "CHECKPOINT", "CURRENT_USER", "RECONFIGURE", "TSEQUAL", "SOME", "AVG", "CURRENT_TRANSFORM_GROUP_FOR_TYPE", "FREE", "ITERATE", "MOD", "CORR", "CURRENT_DATE", "INNER", "SCOPE", "TIMEZONE_HOUR", "EXIT", "NATURAL", "KEY", "UESCAPE", "VARIABLE", "XMLATTRIBUTES", "BINARY", "INTERSECT", "PRIVILEGES", "RAISERROR", "TRANSLATION", "END", "FLOAT", "FREETEXT", "SQLCODE", "DATABASE", "TRANSLATE_REGEX", "VALUE", "SCROLL", "SEMANTICSIMILARITYDETAILSTABLE", "SPECIFIC", "TERMINATE", "ALTER", "DISTINCT", "MODULE", "PRIOR", "PUBLIC", "CURRENT_SCHEMA", "CURRENT_TIME", "DESCRIBE", "FREETEXTTABLE", "LIKE_REGEX", "COALESCE", "MAP", "READ", "UPDATE", "WITHIN", "DELETE", "REFERENCES", "PREORDER", "STATE", "USAGE", "CONTAINS", "FULL", "INTERSECTION", "NONE", "SUBSTRING", "ARE", "BREADTH", "EXCEPTION", "EXTRACT", "LESS", "MAX", "OVERLAY", "SUBMULTISET"], "BQ": ["CUBE", "RANGE", "WHEN", "RIGHT", "SET", "TREAT", "ASC", "CONTAINS", "CREATE", "ELSE", "FETCH", "FROM", "HASH", "LIKE", "WINDOW", "ROWS", "TABLESAMPLE", "TO", "CURRENT", "DEFINE", "LATERAL", "OF", "ORDER", "ESCAPE", "TRUE", "USING", "ARRAY", "BY", "CROSS", "RECURSIVE", "SELECT", "DEFAULT", "OR", "OVER", "PARTITION", "WHERE", "UNION", "FOR", "INTO", "JOIN", "ROLLUP", "SOME", "LOOKUP", "NULLS", "ON", "AND", "ANY", "EXCEPT", "EXISTS", "HAVING", "WITHIN", "COLLATE", "DISTINCT", "ENUM", "INTERSECT", "IS", "NATURAL", "PRECEDING", "AT", "EXTRACT", "FALSE", "IN", "MERGE", "STRUCT", "CAST", "IGNORE", "INNER", "NEW", "OUTER", "INTERVAL", "LIMIT", "RESPECT", "UNNEST", "WITH", "DESC", "EXCLUDE", "GROUPS", "PROTO", "THEN", "ASSERT_ROWS_MODIFIED", "END", "FULL", "GROUPING", "NOT", "GROUP", "IF", "LEFT", "ALL", "AS", "BETWEEN", "CASE", "FOLLOWING", "NO", "NULL", "UNBOUNDED"], "DELTALAKE": ["EXTERNAL", "EXTRACT", "PRIMARY", "SEMI", "UNIQUE", "ALL", "AT", "JOIN", "PARTITION", "USING", "ELSE", "REFERENCES", "SOME", "BETWEEN", "END", "FOREIGN", "GRANT", "GROUPING", "INNER", "WINDOW", "CURRENT_TIME", "INSERT", "LOCAL", "ORDER", "SELECT", "COMMIT", "EXISTS", "OR", "WHEN", "AS", "FROM", "OF", "ROLLUP", "GLOBAL", "NO", "SET", "START", "TO", "CAST", "FALSE", "OVERLAPS", "ROW", "ROWS", "THEN", "UPDATE", "ANY", "CREATE", "DESCRIBE", "REVOKE", "TRAILING", "TRUE", "AUTHORIZATION", "CASE", "EVENT_DATE", "HAVING", "ALTER", "FUNCTION", "MINUS", "OUT", "RANGE", "USER", "WHERE", "ANTI", "LEADING", "NATURAL", "VALUES", "CURRENT_USER", "GROUP", "OUTER", "ROLLBACK", "SESSION_USER", "CHECK", "LIKE", "TABLE", "DELETE", "ESCAPE", "COLLATE", "COLUMN", "POSITION", "CONSTRAINT", "EXCEPT", "NOT", "RIGHT", "FULL", "ONLY", "TRUNCATE", "CROSS", "CURRENT", "DISTINCT", "FOR", "INTERVAL", "LATERAL", "WITH", "FILTER", "IN", "ON", "UNKNOWN", "FETCH", "TIME", "CUBE", "UNION", "CURRENT_DATE", "INTERSECT", "BOTH", "NULL", "AND", "ARRAY", "BY", "CURRENT_TIMESTAMP", "IS", "TABLESAMPLE", "DROP", "INTO", "LEFT"], "GCS_DATALAKE": ["STRUCT", "COLLATE", "FOLLOWING", "INTERSECT", "NO", "OF", "RECURSIVE", "TREAT", "CONTAINS", "ELSE", "END", "GROUPS", "INNER", "IS", "ARRAY", "AS", "HAVING", "NATURAL", "ORDER", "PRECEDING", "PROTO", "AND", "BY", "ESCAPE", "IN", "LEFT", "OR", "TABLESAMPLE", "THEN", "CREATE", "FOR", "LIKE", "UNION", "DEFINE", "FROM", "UNBOUNDED", "DISTINCT", "EXCLUDE", "GROUPING", "LOOKUP", "OUTER", "RIGHT", "CASE", "CURRENT", "JOIN", "NULLS", "USING", "WITHIN", "AT", "FALSE", "INTERVAL", "INTO", "ALL", "CROSS", "ENUM", "EXCEPT", "EXTRACT", "FETCH", "UNNEST", "WHERE", "CUBE", "LATERAL", "PARTITION", "ROLLUP", "SELECT", "SOME", "EXISTS", "NULL", "OVER", "WHEN", "WINDOW", "ASC", "BETWEEN", "DESC", "HASH", "ANY", "CAST", "IF", "MERGE", "SET", "TRUE", "ASSERT_ROWS_MODIFIED", "FULL", "LIMIT", "NEW", "ON", "RANGE", "TO", "WITH", "DEFAULT", "GROUP", "IGNORE", "NOT", "RESPECT", "ROWS"], "MSSQL": ["WHERE", "ASYMMETRIC", "DICTIONARY", "ELSE", "FREETEXT", "NEW", "PARAMETERS", "USE", "WHILE", "REVOKE", "COALESCE", "CONSTRAINTS", "CONTAINSTABLE", "DUMP", "LOCATOR", "NAMES", "PIVOT", "UNPIVOT", "CASE", "CURSOR", "DETERMINISTIC", "EVERY", "EXEC", "HOUR", "INCLUDE", "DECIMAL", "IDENTITYCOL", "SYMMETRIC", "AFTER", "DEFERRABLE", "FETCH", "IMMEDIATE", "OVERLAY", "ASSERTION", "CONVERT", "LIMIT", "MERGE", "PRIVILEGES", "BIT_LENGTH", "BOTH", "IF", "LOWER", "RIGHT", "MEMBER", "MODULE", "RECURSIVE", "ACTION", "PROCEDURE", "REVERT", "UNNEST", "AUTHORIZATION", "CHAR_LENGTH", "REFERENCES", "SPECIFICTYPE", "UNION", "VARCHAR", "CARDINALITY", "CLOB", "RANGE", "SYSTEM_USER", "THEN", "CURRENT_TIMESTAMP", "FUSION", "INITIALIZE", "LARGE", "NULLIF", "PREPARE", "CONSTRAINT", "CONTINUE", "SIZE", "TO", "UPPER", "ANY", "ASC", "DATE", "INDEX", "THAN", "PROC", "BROWSE", "DEALLOCATE", "EXECUTE", "EXTERNAL", "INDICATOR", "INOUT", "OVER", "ESCAPE", "MIN", "NUMERIC", "TRUNCATE", "DATA", "FILE", "FOREIGN", "CALLED", "OPEN", "XMLQUERY", "CHECKPOINT", "DBCC", "DECLARE", "FREETEXTTABLE", "POSITION_REGEX", "XMLCONCAT", "CONNECT", "IS", "OPENDATASOURCE", "ARE", "NONE", "ZONE", "COMPUTE", "SQLWARNING", "VALUE", "DELETE", "TEMPORARY", "TSEQUAL", "WRITE", "CALL", "SUBMULTISET", "BREAK", "EACH", "FUNCTION", "RESTRICT", "START", "ERRLVL", "IN", "PRESERVE", "DISTRIBUTED", "MULTISET", "OUT", "ELEMENT", "INTERVAL", "OFF", "POSTFIX", "ROLLUP", "STATIC", "CHARACTER_LENGTH", "MINUTE", "XMLVALIDATE", "DESCRIBE", "OPENROWSET", "OPENXML", "REGR_SLOPE", "REPLICATION", "COVAR_SAMP", "CORR", "END-EXEC", "TRAN", "DESTROY", "OFFSETS", "WAITFOR", "AND", "CURRENT_ROLE", "INTERSECTION", "SCROLL", "TIMEZONE_MINUTE", "BULK", "FALSE", "FULLTEXTTABLE", "INSENSITIVE", "UPDATE", "SHUTDOWN", "AS", "DESTRUCTOR", "GENERAL", "GO", "ON", "OPENQUERY", "SCHEMA", "STATE", "GRANT", "SEARCH", "SELECT", "XMLCOMMENT", "DESCRIPTOR", "SQL", "COUNT", "OCTET_LENGTH", "SIMILAR", "COLLATION", "CREATE", "INT", "INTO", "MAX", "SETUSER", "WINDOW", "ATOMIC", "EXCEPT", "LOCALTIMESTAMP", "SECURITYAUDIT", "SEMANTICSIMILARITYDETAILSTABLE", "CURRENT_DATE", "SPACE", "VAR_SAMP", "CONDITION", "DEREF", "NCHAR", "PUBLIC", "ALLOCATE", "SENSITIVE", "RELATIVE", "SECOND", "XMLELEMENT", "SCOPE", "TABLESAMPLE", "TRAILING", "PERCENTILE_CONT", "ROWGUIDCOL", "TEXTSIZE", "VALUES", "BEGIN", "ITERATE", "OBJECT", "ONLY", "PARTITION", "RELEASE", "EXTRACT", "NEXT", "OLD", "RECONFIGURE", "TRIGGER", "BETWEEN", "INSERT", "LEVEL", "SYSTEM", "DISCONNECT", "NATURAL", "SEQUENCE", "CURRENT_TRANSFORM_GROUP_FOR_TYPE", "DYNAMIC", "GLOBAL", "METHOD", "UNDER", "AGGREGATE", "LIKE_REGEX", "NOT", "STRUCTURE", "SUBSTRING", "CURRENT_PATH", "SEMANTICSIMILARITYTABLE", "CURRENT_CATALOG", "OCCURRENCES_REGEX", "TRANSLATION", "WITHIN", "XMLAGG", "REGR_R2", "XMLFOREST", "SMALLINT", "ALL", "CHARACTER", "CLASS", "COLLECT", "CURRENT_USER", "DOMAIN", "FIRST", "VARYING", "ASENSITIVE", "INTEGER", "ORDER", "SQLEXCEPTION", "USING", "FOUND", "LINENO", "DROP", "FULL", "PRECISION", "SAVEPOINT", "TRANSLATE_REGEX", "VARIABLE", "XMLCAST", "DEC", "FOR", "HOLDLOCK", "RESULT", "OPTION", "PASCAL", "READTEXT", "RESTORE", "SAVE", "XMLTEXT", "BIT", "MODIFIES", "SQLERROR", "KEY", "NULL", "SPECIFIC", "SUM", "NCLOB", "NORMALIZE", "REGR_AVGY", "TRUE", "WITHINGROUP", "CONNECTION", "CUBE", "INITIALLY", "POSITION", "ROW", "SESSION", "XMLSERIALIZE", "LATERAL", "XMLEXISTS", "IDENTITY", "LESS", "MATCH", "PERCENTILE_DISC", "STDDEV_SAMP", "COMPLETION", "DEFAULT", "GOTO", "INPUT", "PRIMARY", "UESCAPE", "FLOAT", "LEADING", "REAL", "USAGE", "CORRESPONDING", "DAY", "RETURN", "XMLNAMESPACES", "XMLPI", "BY", "JOIN", "TERMINATE", "HOLD", "ROWS", "BLOB", "CYCLE", "DOUBLE", "GROUP", "LOCALTIME", "PARTIAL", "ROLE", "CATALOG", "LOAD", "PARAMETER", "XMLDOCUMENT", "END", "GROUPING", "PREORDER", "CAST", "XMLATTRIBUTES", "XMLBINARY", "COLUMN", "REGR_SYY", "CONTAINS", "CURRENT_SCHEMA", "NO", "ROWCOUNT", "LIKE", "OUTER", "BEFORE", "FROM", "ORDINALITY", "STATEMENT", "BINARY", "COVAR_POP", "LAST", "WORK", "HAVING", "KILL", "SECTION", "WITHOUT", "GET", "LEFT", "TOP", "TRANSLATE", "TABLE", "BOOLEAN", "DISK", "INNER", "MONTH", "NATIONAL", "REGR_COUNT", "ROLLBACK", "VIEW", "CHAR", "MOD", "PLAN", "SETS", "REF", "TRANSACTION", "YEAR", "OVERLAPS", "TRY_CONVERT", "DATABASE", "IDENTITY_INSERT", "PERCENT", "WIDTH_BUCKET", "CURRENT", "CURRENT_TIME", "EQUALS", "FILTER", "INTERSECT", "SOME", "WHEN", "XMLPARSE", "CUME_DIST", "LOCAL", "XMLITERATE", "DIAGNOSTICS", "PERCENT_RANK", "REGR_AVGX", "REGR_INTERCEPT", "RETURNS", "PAD", "OPERATION", "ARRAY", "AT", "COLLATE", "CROSS", "DENY", "EXIT", "NONCLUSTERED", "WHENEVER", "CASCADE", "FORTRAN", "OR", "UNKNOWN", "CASCADED", "HOST", "PRIOR", "NOCHECK", "OF", "READ", "SQLCODE", "TREAT", "CURRENT_DEFAULT_TRANSFORM_GROUP", "REFERENCING", "TIMEZONE_HOUR", "UPDATETEXT", "USER", "PRINT", "REGR_SXY", "SEMANTICKEYPHRASETABLE", "SESSION_USER", "VAR_POP", "ADMIN", "BACKUP", "EXCEPTION", "FILLFACTOR", "LN", "READS", "ADA", "ALIAS", "CLOSE", "ISOLATION", "SQLCA", "STDDEV_POP", "WRITETEXT", "DESC", "SET", "UNIQUE", "ABSOLUTE", "OUTPUT", "RULE", "ALTER", "SQLSTATE", "TRIM", "BREADTH", "MAP", "COMMIT", "FREE", "RAISERROR", "SUBSTRING_REGEX", "WITH", "XMLTABLE", "AVG", "DEFERRED", "DISTINCT", "REGR_SXX", "MODIFY", "STATISTICS", "ADD", "EXISTS", "LANGUAGE", "PREFIX", "DEPTH", "CHECK", "CLUSTERED", "IGNORE", "ROUTINE", "TIME"], "POSTGRES": ["NATURAL", "SIMILAR", "ASC", "BINARY", "DEFERRABLE", "EXCEPT", "FREEZE", "LEADING", "LEFT", "ANALYZE", "CURRENT_ROLE", "CURRENT_TIME", "DO", "IS", "OVERLAPS", "OUTER", "UNIQUE", "VERBOSE", "CURRENT_DATE", "CURRENT_USER", "NOT", "NOTNULL", "PRIMARY", "SESSION_USER", "BETWEEN", "CONSTRAINT", "FOREIGN", "ORDER", "ONLY", "OR", "COLUMN", "DEFAULT", "INNER", "OFFSET", "GROUP", "LOCALTIME", "NEW", "AND", "AUTHORIZATION", "CREATE", "DISTINCT", "CHECK", "ILIKE", "ASYMMETRIC", "CURRENT_TIMESTAMP", "INTERSECT", "SYMMETRIC", "TABLE", "USER", "ELSE", "FULL", "IN", "SELECT", "FROM", "GRANT", "INTO", "LIKE", "ALL", "ANALYSE", "END", "FALSE", "RIGHT", "SOME", "OLD", "TRUE", "WHEN", "BOTH", "HAVING", "LOCALTIMESTAMP", "OFF", "CROSS", "UNION", "CASE", "DESC", "REFERENCES", "THEN", "ON", "PLACING", "USING", "WHERE", "ANY", "ISNULL", "JOIN", "LIMIT", "FOR", "INITIALLY", "NULL", "TO", "ARRAY", "AS", "CAST", "COLLATE", "TRAILING"], "RS": ["TRUNCATECOLUMNS", "UNIQUE", "TEXT32K", "COLLATE", "IN", "OLD", "REFERENCES", "AES256", "LZOP", "RESPECT", "WALLET", "AND", "TOP", "SYSDATE", "IGNORE", "LEADING", "MOSTLY32", "NOTNULL", "OUTER", "TRAILING", "WHEN", "BACKUP", "GRANT", "WITH", "ANY", "FOREIGN", "GLOBALDICT256", "ILIKE", "RESORT", "BETWEEN", "DESC", "LOCALTIMESTAMP", "NULL", "RECOVER", "CURRENT_DATE", "BOTH", "CASE", "CREDENTIALS", "NEW", "OVERLAPS", "AZ64", "ASC", "DISABLE", "EXPLICIT", "ISNULL", "AS", "DELTA32K", "FREEZE", "INTO", "LUN", "MOSTLY8", "BZIP2", "ALL", "CAST", "DEFLATE", "DELTA", "END", "PERMISSIONS", "SESSION_USER", "AES128", "ENABLE", "OPEN", "RESTORE", "ELSE", "DEFAULT", "ONLY", "TEXT255", "CURRENT_USER", "EXCEPT", "GROUP", "IS", "TDES", "VERBOSE", "ANALYZE", "ENCODE", "ENCRYPT     ", "FROM", "SNAPSHOT ", "UNION", "ALLOWOVERWRITE", "LZO", "OR", "TABLE", "TAG", "BLANKSASNULL", "CURRENT_TIME", "ENCRYPTION", "FALSE", "JOIN", "BYTEDICT", "NULLS", "DEFRAG", "LUNS", "OFF", "OFFSET", "SOME", "COLUMN", "DO", "FULL", "BINARY", "LIKE", "OFFLINE", "THEN", "INNER", "CHECK", "NOT", "ARRAY", "DISTINCT", "INITIALLY", "ON", "PERCENT", "RAW", "SYSTEM", "DEFERRABLE", "CURRENT_USER_ID", "FOR", "INTERSECT", "LEFT", "PLACING", "USER", "CROSS", "LIMIT", "PARALLEL", "EMPTYASNULL", "OID", "REJECTLOG", "CREATE", "HAVING", "MOSTLY13", "PRIMARY", "READRATIO", "RIGHT", "USING", "WHERE", "GZIP", "GLOBALDICT64K", "IDENTITY", "LANGUAGE", "MINUS", "NATURAL", "AUTHORIZATION", "CONSTRAINT", "LOCALTIME", "PARTITION", "TRUE", "ANALYSE", "TO", "UUID", "SELECT", "ORDER", "SIMILAR", "WITHOUT", "CURRENT_TIMESTAMP"], "S3_DATALAKE": ["AS", "CHAR", "COMMIT", "FOLLOWING", "REDUCE", "ROW", "WHERE", "BY", "LOCAL", "CURRENT_DATE", "JOIN", "OUT", "PRESERVE", "READS", "ELSE", "WITH", "CROSS", "EXISTS", "GRANT", "PRECEDING", "TIMESTAMP", "UNBOUNDED", "DROP", "START", "TABLE", "FLOOR", "FULL", "HAVING", "IF", "LESS", "RIGHT", "ROLLUP", "UTC_TIMESTAMP", "CURRENT_TIMESTAMP", "OR", "OVER", "BETWEEN", "DISTINCT", "INTEGER", "BIGINT", "COLUMN", "FETCH", "MACRO", "UNIQUEJOIN", "ARRAY", "CASHE", "EXTRACT", "REFERENCES", "INSERT", "LIKE", "VALUES", "TRUE", "BOOLEAN", "EXCHANGE", "IN", "INNER", "LATERAL", "PRECISION", "TRIGGER", "BINARY", "CAST", "DOUBLE", "END", "FALSE", "ON", "PRIMARY", "CREATE", "DECIMAL", "FLOAT", "NULL", "TABLESAMPLE", "THEN", "REVOKE", "AND", "DELETE", "LEFT", "NONE", "PERCENT", "PROCEDURE", "REGEXP", "TRUNCATE", "UNION", "BOTH", "MORE", "VARCHAR", "ALL", "DATE", "SELECT", "CURSOR", "FUNCTION", "RLIKE", "NUMERIC", "OF", "AUTHORIZATION", "CASE", "CONF", "FOR", "GROUP", "INTERSECT", "ROLLBACK", "DATABASE", "INT", "NOT", "SMALLINT", "WHEN", "CONSTRAINT", "IMPORT", "OUTER", "RANGE", "TIME", "EXTENDED", "INTO", "CUBE", "FOREIGN", "INTERVAL", "PARTIALSCAN", "SET", "TO", "CURRENT", "FROM", "VIEWS", "ALTER", "DESCRIBE", "EXTERNAL", "GROUPING", "IS", "PARTITION", "USING", "DAYOFWEEK", "MAP", "TRANSFORM", "USER", "ONLY", "ROWS", "ORDER", "UPDATE", "WINDOW"], "SNOWFLAKE": ["SET", "TABLE", "GRANT", "INNER", "MINUS", "REGEXP", "CONNECTION", "CROSS", "START", "IN", "DELETE", "DISTINCT", "EXISTS", "FROM", "UPDATE", "DATABASE", "FULL", "GROUP", "INTO", "FALSE", "INSERT", "NOT", "ORDER", "ACCOUNT", "ANY", "BETWEEN", "CAST", "TRIGGER", "USING", "ILIKE", "NULL", "ON", "OR", "ALL", "CONNECT", "REVOKE", "WHERE", "SAMPLE", "SELECT", "CHECK", "JOIN", "RLIKE", "ROW", "CURRENT", "LATERAL", "NATURAL", "WHENEVER", "LIKE", "SOME", "TRUE", "VIEW", "CASE", "CURRENT_TIMESTAMP", "INCREMENT", "IS", "ELSE", "FOLLOWING", "UNION", "AND", "BY", "CREATE", "CURRENT_TIME", "TO", "CURRENT_DATE", "DROP", "ISSUE", "SCHEMA", "QUALIFY", "ROWS", "TABLESAMPLE", "UNIQUE", "CONSTRAINT", "GSCLUSTER", "HAVING", "ORGANIZATION", "COLUMN", "FOR", "INTERSECT", "OF", "THEN", "VALUES", "WITH", "ALTER", "CURRENT_USER", "LOCALTIME", "RIGHT", "WHEN", "AS", "LEFT", "LOCALTIMESTAMP", "TRY_CAST"], "SNOWPIPE_STREAMING": ["SET", "TABLE", "GRANT", "INNER", "MINUS", "REGEXP", "CONNECTION", "CROSS", "START", "IN", "DELETE", "DISTINCT", "EXISTS", "FROM", "UPDATE", "DATABASE", "FULL", "GROUP", "INTO", "FALSE", "INSERT", "NOT", "ORDER", "ACCOUNT", "ANY", "BETWEEN", "CAST", "TRIGGER", "USING", "ILIKE", "NULL", "ON", "OR", "ALL", "CONNECT", "REVOKE", "WHERE", "SAMPLE", "SELECT", "CHECK", "JOIN", "RLIKE", "ROW", "CURRENT", "LATERAL", "NATURAL", "WHENEVER", "LIKE", "SOME", "TRUE", "VIEW", "CASE", "CURRENT_TIMESTAMP", "INCREMENT", "IS", "ELSE", "FOLLOWING", "UNION", "AND", "BY", "CREATE", "CURRENT_TIME", "TO", "CURRENT_DATE", "DROP", "ISSUE", "SCHEMA", "QUALIFY", "ROWS", "TABLESAMPLE", "UNIQUE", "CONSTRAINT", "GSCLUSTER", "HAVING", "ORGANIZATION", "COLUMN", "FOR", "INTERSECT", "OF", "THEN", "VALUES", "WITH", "ALTER", "CURRENT_USER", "LOCALTIME", "RIGHT", "WHEN", "AS", "LEFT", "LOCALTIMESTAMP", "TRY_CAST"], "CLICKHOUSE": []}